<template>
  <BaseQueryComponent
    title="安全费台账"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

defineEmits(['back'])

const queryFields = [
  {
    key: 'expenseNumber',
    label: '费用单号',
    type: 'text',
    placeholder: '请输入费用单号',
    width: '180px'
  },
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    placeholder: '请输入项目名称',
    width: '200px'
  },
  {
    key: 'expenseDate',
    label: '支出日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'amount',
    label: '支出金额',
    type: 'amount-range'
  },
  {
    key: 'expenseType',
    label: '费用类型',
    type: 'select',
    placeholder: '请选择费用类型',
    width: '150px',
    options: [
      { label: '安全设备', value: 'equipment' },
      { label: '安全培训', value: 'training' },
      { label: '安全检查', value: 'inspection' },
      { label: '应急处理', value: 'emergency' },
      { label: '其他', value: 'other' }
    ]
  }
]

const mockData = [
  ['费用单号', '项目名称', '费用类型', '支出金额', '支出日期', '用途说明', '供应商', '经办人', '审批人', '备注'],
  ['AQ202401001', '办公楼建设', '安全设备', '25000.00', '2024-01-10', '安全帽、安全带采购', '劳保用品公司', '张三', '李经理', ''],
  ['AQ202401002', '厂房改造', '安全培训', '8000.00', '2024-01-15', '施工人员安全培训', '培训机构A', '王五', '李经理', '40人次'],
  ['AQ202401003', '道路施工', '安全检查', '5000.00', '2024-01-20', '第三方安全检查', '检测公司B', '赵六', '张主管', '月度检查'],
  ['AQ202401004', '设备安装', '应急处理', '12000.00', '2024-01-25', '应急救援设备', '医疗器械公司', '孙七', '李经理', '急救包、担架'],
  ['AQ202401005', '装修工程', '安全设备', '18000.00', '2024-01-30', '防护网、警示标识', '安全设备厂', '周八', '张主管', '高空作业防护']
]
</script>
