<template>
  <div class="treasury-export-container">
    <!-- 导出一体化合同台账 -->
    <div class="export-form">
      <h2>导出一体化合同台账</h2>
      <el-form label-width="120px">
        <el-form-item>
          <el-button type="primary" @click="exportIntegratedContract" :loading="integratedContractLoading">
            <el-icon><Download /></el-icon>
            {{ integratedContractLoading ? '导出中...' : '导出一体化合同台账' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 导出一体化预付款及保证金合同台账 -->
    <div class="export-form">
      <h2>导出一体化预付款及保证金合同台账</h2>
      <el-form label-width="120px">
        <el-form-item>
          <div class="button-group">
            <el-button type="success" @click="exportPrepaymentContract" :loading="prepaymentContractLoading">
              <el-icon><Download /></el-icon>
              {{ prepaymentContractLoading ? '导出中...' : '重新开始' }}
            </el-button>
            <el-button type="primary" @click="updatePrepaymentContract" :loading="updatePrepaymentContractLoading">
              <el-icon><Refresh /></el-icon>
              {{ updatePrepaymentContractLoading ? '更新中...' : '失败继续' }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Refresh } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const integratedContractLoading = ref(false)
const prepaymentContractLoading = ref(false)
const updatePrepaymentContractLoading = ref(false)
const dialogVisible = ref(false)

// 导出一体化合同台账
const exportIntegratedContract = async () => {
  integratedContractLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '导出一体化合同台账',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('一体化合同台账导出任务已启动')
    } else {
      ElMessage.error('一体化合同台账导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    integratedContractLoading.value = false
  }
}

// 导出一体化预付款及保证金合同台账 - 生成报表
const exportPrepaymentContract = async () => {
  prepaymentContractLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '导出一体化预付款及保证金台账重新开始',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('预付款及保证金合同台账生成任务已启动')
    } else {
      ElMessage.error('预付款及保证金合同台账生成失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    prepaymentContractLoading.value = false
  }
}

// 导出一体化预付款及保证金合同台账 - 更新开票
const updatePrepaymentContract = async () => {
  updatePrepaymentContractLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '导出一体化预付款及保证金台账失败继续',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('预付款及保证金合同台账更新任务已启动')
    } else {
      ElMessage.error('预付款及保证金合同台账更新失败')
    }
  } catch (error) {
    ElMessage.error('更新过程中发生错误: ' + error.message)
  } finally {
    updatePrepaymentContractLoading.value = false
  }
}
</script>

<style scoped>
.treasury-export-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
  position: relative;
}

.export-form {
  max-width: 600px;
  margin: 0 auto 40px auto;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.export-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.export-form:last-of-type {
  margin-bottom: 0;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.button-group .el-button {
  flex: 1;
  min-width: 140px;
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }
  
  .button-group .el-button {
    width: 100%;
  }
}
</style>