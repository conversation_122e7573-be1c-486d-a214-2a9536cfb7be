<template>
  <div class="universal-table-component">
    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">处理中...</div>
    </div>

    <!-- Univer 容器 -->
    <div class="univer-container" :class="{ 'loading': isLoading }">
      <div ref="univerContainer" class="univer-spreadsheet"></div>
      
      <!-- 浮动控制面板 -->
      <div class="floating-control-panel">
        <div class="panel-controls">
          <button @click="triggerImport" class="control-button import small" :disabled="isLoading">
            <span class="button-icon">📥</span>
            <span>导入</span>
          </button>
          <button @click="exportData" class="control-button export small" :disabled="isLoading">
            <span class="button-icon">📤</span>
            <span>导出</span>
          </button>
          <!-- 隐藏的文件输入框 -->
          <input ref="fileInput" type="file" accept=".xlsx,.xls" @change="handleFileImport" style="display: none" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { createUniver, defaultTheme, LocaleType, merge } from '@univerjs/presets'
import { UniverSheetsCorePreset } from '@univerjs/presets/preset-sheets-core'
import UniverPresetSheetsCoreZhCN from '@univerjs/presets/preset-sheets-core/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-core.css'

import { UniverSheetsConditionalFormattingPreset } from '@univerjs/presets/preset-sheets-conditional-formatting'
import sheetsConditionalFormattingZhCN from '@univerjs/presets/preset-sheets-conditional-formatting/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-conditional-formatting.css'

import { UniverSheetsDataValidationPreset } from '@univerjs/presets/preset-sheets-data-validation'
import sheetsDataValidationZhCN from '@univerjs/presets/preset-sheets-data-validation/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-data-validation.css'

import { UniverSheetsFilterPreset } from '@univerjs/presets/preset-sheets-filter'
import sheetsFilterZhCN from '@univerjs/presets/preset-sheets-filter/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-filter.css'

import { UniverSheetsTablePlugin } from '@univerjs/sheets-table'
import { UniverSheetsTableUIPlugin } from '@univerjs/sheets-table-ui'
import SheetsTableUIZhCN from '@univerjs/sheets-table-ui/locale/zh-CN'

import '@univerjs/sheets-table-ui/lib/index.css'
import '@univerjs/sheets-table/facade'

const props = defineProps({
  dataProvider: {
    type: Function,
    default: null
  },
  initialData: {
    type: Object,
    default: () => ({})
  },
  workbookName: {
    type: String,
    default: '通用表格'
  }
})

const emit = defineEmits(['dataChange', 'initialized', 'error'])

// 响应式数据
const univerContainer = ref(null)
const fileInput = ref(null)
let univerInstance = null
let univerAPIInstance = null
const isLoading = ref(false)
const tableData = ref({})
const isInitialized = ref(false)

// 配置
const textColumnConfig = ref({
  keywords: ['身份证号', '证件号码', '银行卡号', '工号'],
  processNumericHeaders: true
})

const styleConfig = ref({
  headerBackgroundColor: '#f0f0f0',
  headerFontWeight: 'bold',
  defaultRows: 10,
  defaultCols: 4, // 减少默认列数，避免创建过多空列
  extraRows: 0,   // 增加2行空行便于数据输入
  extraCols: 0    // 增加1列空列便于数据输入
})

// 处理身份证号和证件号码列为文本格式 - 参考 SalaryTax2View 的实现
const processIdColumnsAsText = (data) => {
  if (!data || data.length === 0) return data

  // 获取表头行
  const headerRow = data[0]
  if (!headerRow) return data

  // 找出需要处理为文本的列的索引
  const textColumns = []
  headerRow.forEach((header, index) => {
    if (header && typeof header === 'string') {
      const headerStr = header.trim()

      // 检查是否包含配置的关键词
      const hasKeyword = textColumnConfig.value.keywords.some(keyword =>
        headerStr.includes(keyword)
      )

      // 检查是否为纯数字列名（如果配置允许）
      const isNumericHeader = textColumnConfig.value.processNumericHeaders &&
        /^\d+$/.test(headerStr)

      if (hasKeyword || isNumericHeader) {
        textColumns.push(index)
      }
    }
  })

  // 如果没有找到需要处理的列，直接返回原数据
  if (textColumns.length === 0) return data

  // 处理数据，确保指定列为文本格式 - 使用Univer字典对象格式
  return data.map((row, rowIndex) => {
    if (rowIndex === 0) return row // 保持表头不变

    return row.map((cell, colIndex) => {
      if (textColumns.includes(colIndex) && cell !== null && cell !== undefined) {
        const cellValue = String(cell).trim()
        // 对于需要处理的列，使用Univer字典对象格式强制为文本
        if (cellValue !== '') {
          return {
            v: cellValue,
            t: 1, // CellValueType.STRING
            s: null
          }
        }
      }
      return cell
    })
  })
}

// 验证表格数据格式
const validateTableData = (data) => {
  if (!data || typeof data !== 'object') return false

  for (const [tableName, tableContent] of Object.entries(data)) {
    if (typeof tableName !== 'string') return false
    if (!Array.isArray(tableContent)) return false

    // 检查每行是否为数组
    for (const row of tableContent) {
      if (!Array.isArray(row)) return false
    }
  }

  return true
}

// 格式化错误消息
const formatErrorMessage = (error, details) => {
  const ERROR_MESSAGES = {
    'INIT_FAILED': 'Univer 初始化失败',
    'LOAD_DATA_FAILED': '数据加载失败',
    'DATA_PROVIDER_FAILED': '数据获取失败',
    'INVALID_DATA_FORMAT': '数据格式无效',
    'UNIVER_NOT_INITIALIZED': 'Univer 未初始化',
    'EXPORT_FAILED': '数据导出失败',
    'IMPORT_FAILED': '数据导入失败'
  }
  const baseMessage = ERROR_MESSAGES[error] || '未知错误'
  return details ? `${baseMessage}: ${details}` : baseMessage
}

// 初始化 Univer
const initUniver = async () => {
  if (!univerContainer.value) return

  try {
    isLoading.value = true

    const { univer, univerAPI } = createUniver({
      locale: LocaleType.ZH_CN,
      locales: {
        [LocaleType.ZH_CN]: merge(
          {},
          UniverPresetSheetsCoreZhCN,
          sheetsConditionalFormattingZhCN,
          sheetsDataValidationZhCN,
          sheetsFilterZhCN,
          SheetsTableUIZhCN,
        )
      },
      theme: defaultTheme,
      presets: [
        UniverSheetsCorePreset({
          container: univerContainer.value,
          header: true,
          footer: true
        }),
        UniverSheetsConditionalFormattingPreset(),
        UniverSheetsDataValidationPreset(),
        UniverSheetsFilterPreset()
      ]
    })

    univer.registerPlugin(UniverSheetsTablePlugin)
    univer.registerPlugin(UniverSheetsTableUIPlugin)

    univerInstance = univer
    univerAPIInstance = univerAPI

    // 创建工作簿
    const workbook = univerAPIInstance.createWorkbook({
      id: 'universal-table-workbook',
      name: props.workbookName
    })

    // 删除所有默认工作表
    try {
      const sheets = workbook.getSheets()
      sheets.forEach(sheet => {
        try {
          workbook.deleteSheet(sheet.getSheetId())
        } catch (error) {
          console.warn('删除工作表失败:', error)
        }
      })
    } catch (error) {
      console.warn('删除默认工作表失败:', error)
    }

    // 不创建默认工作表，等待数据加载时创建

    console.log('Univer初始化成功')
    isInitialized.value = true
    emit('initialized')
  } catch (error) {
    console.error('Univer初始化失败:', error)
    const errorMsg = formatErrorMessage('INIT_FAILED', error.message)
    emit('error', errorMsg)
  } finally {
    isLoading.value = false
  }
}

// 加载数据到表格
const loadData = async (data) => {
  if (!univerAPIInstance) {
    const errorMsg = formatErrorMessage('UNIVER_NOT_INITIALIZED')
    emit('error', errorMsg)
    return
  }

  if (!validateTableData(data)) {
    const errorMsg = formatErrorMessage('INVALID_DATA_FORMAT')
    emit('error', errorMsg)
    return
  }

  try {
    isLoading.value = true

    const workbook = univerAPIInstance.getActiveWorkbook()
    if (!workbook) {
      throw new Error('无法获取工作簿实例')
    }

    const tableNames = Object.keys(data)
    const existingSheets = workbook.getSheets()

    // 策略：更新现有工作表，删除多余的，创建缺少的

    // 1. 更新或创建需要的工作表
    let firstSheetId = null
    for (let i = 0; i < tableNames.length; i++) {
      const tableName = tableNames[i]
      const tableContent = data[tableName]

      // 查找是否已存在同名工作表
      const existingSheet = existingSheets.find(sheet =>
        sheet.getSheet().getName() === tableName
      )

      let sheet
      if (existingSheet) {
        // 更新现有工作表
        sheet = existingSheet
        console.log(`更新现有工作表: ${tableName}`)

        // 清空现有数据
        const maxRow = sheet.getMaxRows()
        const maxCol = sheet.getMaxColumns()
        if (maxRow > 0 && maxCol > 0) {
          sheet.getRange(0, 0, maxRow, maxCol).clear()
        }

        // 检查是否需要扩展行数和列数
        if (tableContent && tableContent.length > 0) {
          const requiredRows = Math.max(
            tableContent.length + styleConfig.value.extraRows,
            styleConfig.value.defaultRows
          )
          const requiredCols = Math.max(
            (tableContent[0]?.length || 0) + styleConfig.value.extraCols,
            styleConfig.value.defaultCols
          )

          // 扩展行数
          if (requiredRows > maxRow) {
            const rowsToAdd = requiredRows - maxRow
            console.log(`扩展工作表 ${tableName} 行数: ${maxRow} -> ${requiredRows} (增加${rowsToAdd}行)`)
            for (let r = 0; r < rowsToAdd; r++) {
              sheet.insertRowAfter(maxRow + r - 1)
            }
          }

          // 扩展列数
          if (requiredCols > maxCol) {
            const colsToAdd = requiredCols - maxCol
            console.log(`扩展工作表 ${tableName} 列数: ${maxCol} -> ${requiredCols} (增加${colsToAdd}列)`)
            for (let c = 0; c < colsToAdd; c++) {
              sheet.insertColumnAfter(maxCol + c - 1)
            }
          }
        }
      } else {
        // 创建新工作表
        const requiredRows = Math.max(
          (tableContent?.length || 0) + styleConfig.value.extraRows,
          styleConfig.value.defaultRows
        )
        const requiredCols = Math.max(
          (tableContent?.[0]?.length || 0) + styleConfig.value.extraCols,
          styleConfig.value.defaultCols
        )

        sheet = workbook.create(tableName, requiredRows, requiredCols)
        console.log(`创建新工作表: ${tableName}`)
      }

      if (i === 0) firstSheetId = sheet.getSheetId()

      // 设置数据
      if (tableContent && tableContent.length > 0) {
        const processedData = processIdColumnsAsText(tableContent)
        sheet.getRange(0, 0, processedData.length, processedData[0].length).setValues(processedData)

        // 设置表头样式
        const headerRange = sheet.getRange(0, 0, 1, processedData[0].length)
        headerRange.setFontWeight('bold')
        headerRange.setBackgroundColor(styleConfig.value.headerBackgroundColor)
      }
    }

    // 2. 删除不需要的工作表
    existingSheets.forEach(sheet => {
      const sheetName = sheet.getSheet().getName()
      if (!tableNames.includes(sheetName)) {
        try {
          workbook.deleteSheet(sheet.getSheetId())
          console.log(`删除多余工作表: ${sheetName}`)
        } catch (error) {
          console.warn(`删除工作表 ${sheetName} 失败:`, error)
        }
      }
    })

    // 3. 如果没有数据，确保至少有一个空工作表
    if (tableNames.length === 0) {
      const remainingSheets = workbook.getSheets()
      if (remainingSheets.length === 0) {
        const defaultSheet = workbook.create(
          '空表格',
          styleConfig.value.defaultRows,
          styleConfig.value.defaultCols
        )
        firstSheetId = defaultSheet.getSheetId()
        console.log('创建空的默认工作表')
      } else {
        firstSheetId = remainingSheets[0].getSheetId()
      }
    }

    // 激活第一个工作表
    if (firstSheetId) {
      workbook.setActiveSheet(firstSheetId)
    }

    // 更新本地数据
    tableData.value = { ...data }

    console.log(`成功处理 ${tableNames.length} 个表格`)
  } catch (error) {
    console.error('加载数据失败:', error)
    const errorMsg = formatErrorMessage('LOAD_DATA_FAILED', error.message)
    emit('error', errorMsg)
  } finally {
    isLoading.value = false
  }
}

// 获取当前所有表格数据
const getAllTableData = () => {
  if (!univerAPIInstance) {
    console.warn('Univer 未初始化，无法获取数据')
    return {}
  }

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    if (!workbook) {
      console.warn('无法获取工作簿实例')
      return {}
    }

    const sheets = workbook.getSheets()
    const result = {}

    sheets.forEach(sheet => {
      const sheetName = sheet.getSheet().getName()
      const maxRow = sheet.getMaxRows()
      const maxCol = sheet.getMaxColumns()

      if (maxRow > 0 && maxCol > 0) {
        const data = sheet.getRange(0, 0, maxRow, maxCol).getValues()
        // 过滤掉完全空白的行
        const filteredData = data.filter(row =>
          row.some(cell => {
            // 处理 Univer 单元格对象格式
            if (cell && typeof cell === 'object' && 'v' in cell) {
              return cell.v !== null && cell.v !== undefined && cell.v !== ''
            }
            return cell !== null && cell !== undefined && cell !== ''
          })
        )
        result[sheetName] = filteredData
      } else {
        result[sheetName] = []
      }
    })

    return result
  } catch (error) {
    console.error('获取表格数据失败:', error)
    const errorMsg = formatErrorMessage('LOAD_DATA_FAILED', error.message)
    emit('error', errorMsg)
    return {}
  }
}

// 刷新数据
const refreshData = async () => {
  if (!props.dataProvider) {
    const errorMsg = '未提供数据获取函数'
    emit('error', errorMsg)
    return
  }

  try {
    isLoading.value = true
    const newData = await props.dataProvider()

    if (!validateTableData(newData)) {
      throw new Error('数据提供函数返回的数据格式无效')
    }

    await loadData(newData)

    // 触发数据变化事件
    const currentData = getAllTableData()
    emit('dataChange', currentData)
  } catch (error) {
    console.error('刷新数据失败:', error)
    const errorMsg = formatErrorMessage('DATA_PROVIDER_FAILED', error.message)
    emit('error', errorMsg)
  } finally {
    isLoading.value = false
  }
}

// 导出数据
const exportData = async () => {
  try {
    isLoading.value = true

    const currentData = getAllTableData()

    if (!currentData || Object.keys(currentData).length === 0) {
      throw new Error('没有可导出的数据')
    }

    // 动态导入 exceljs
    const ExcelJS = await import('exceljs')
    const workbook = new ExcelJS.Workbook()

    // 设置工作簿属性
    workbook.creator = '通用表格系统'
    workbook.lastModifiedBy = '通用表格系统'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 为每个表格创建工作表
    Object.entries(currentData).forEach(([tableName, tableData]) => {
      if (!tableData || tableData.length === 0) return

      const worksheet = workbook.addWorksheet(tableName)

      // 添加数据
      tableData.forEach((row, rowIndex) => {
        const processedRow = row.map(cell => {
          // 处理 Univer 单元格对象格式
          if (cell && typeof cell === 'object' && 'v' in cell) {
            return cell.v
          }
          return cell
        })
        worksheet.addRow(processedRow)

        // 设置表头样式
        if (rowIndex === 0) {
          const headerRow = worksheet.getRow(rowIndex + 1)
          headerRow.font = { bold: true }
          headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF0F0F0' }
          }
          headerRow.alignment = { horizontal: 'center', vertical: 'middle' }
        }
      })

      // 自动调整列宽
      worksheet.columns.forEach((column, index) => {
        let maxLength = 0
        worksheet.eachRow((row) => {
          const cell = row.getCell(index + 1)
          const cellValue = cell.value ? String(cell.value) : ''
          maxLength = Math.max(maxLength, cellValue.length)
        })
        column.width = Math.min(Math.max(maxLength + 2, 10), 50)
      })

      // 添加边框
      const range = worksheet.getCell(1, 1).address + ':' +
        worksheet.getCell(tableData.length, tableData[0]?.length || 1).address
      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell, colNumber) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
        })
      })
    })

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
    const filename = `${props.workbookName}_${timestamp}.xlsx`

    // 生成 Excel 文件并下载
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理 URL 对象
    window.URL.revokeObjectURL(url)

    console.log(`成功导出文件: ${filename}`)

    // 触发数据变化事件
    emit('dataChange', currentData)

  } catch (error) {
    console.error('导出数据失败:', error)
    const errorMsg = formatErrorMessage('EXPORT_FAILED', error.message)
    emit('error', errorMsg)
  } finally {
    isLoading.value = false
  }
}

// 触发文件导入
const triggerImport = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

// 处理文件导入
const handleFileImport = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  try {
    isLoading.value = true

    // 验证文件类型
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel' // .xls
    ]

    if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
      throw new Error('请选择有效的Excel文件 (.xlsx 或 .xls)')
    }

    // 动态导入 exceljs
    const ExcelJS = await import('exceljs')
    const workbook = new ExcelJS.Workbook()

    // 读取文件
    const buffer = await file.arrayBuffer()
    await workbook.xlsx.load(buffer)

    // 解析工作表数据
    const importedData = {}

    workbook.worksheets.forEach(worksheet => {
      const sheetName = worksheet.name
      const sheetData = []

      // 获取工作表的实际使用范围
      const rowCount = worksheet.rowCount
      const columnCount = worksheet.columnCount

      if (rowCount > 0 && columnCount > 0) {
        // 遍历所有行
        for (let rowNumber = 1; rowNumber <= rowCount; rowNumber++) {
          const row = worksheet.getRow(rowNumber)
          const rowData = []

          // 遍历所有列
          for (let colNumber = 1; colNumber <= columnCount; colNumber++) {
            const cell = row.getCell(colNumber)
            let cellValue = null

            if (cell.value !== null && cell.value !== undefined) {
              // 处理不同类型的单元格值
              if (typeof cell.value === 'object') {
                // 处理公式、富文本等复杂类型
                if (cell.value.result !== undefined) {
                  cellValue = cell.value.result
                } else if (cell.value.richText) {
                  cellValue = cell.value.richText.map(rt => rt.text).join('')
                } else if (cell.value.text) {
                  cellValue = cell.value.text
                } else {
                  cellValue = String(cell.value)
                }
              } else {
                cellValue = cell.value
              }

              // 处理日期类型
              if (cell.type === ExcelJS.ValueType.Date) {
                cellValue = cell.value.toISOString().split('T')[0]
              }
            }

            rowData.push(cellValue)
          }

          // 检查行是否完全为空
          const hasContent = rowData.some(cell =>
            cell !== null && cell !== undefined && cell !== ''
          )

          if (hasContent) {
            sheetData.push(rowData)
          }
        }
      }

      // 只添加有数据的工作表
      if (sheetData.length > 0) {
        importedData[sheetName] = sheetData
      }
    })

    // 验证导入的数据
    if (Object.keys(importedData).length === 0) {
      throw new Error('Excel文件中没有找到有效数据')
    }

    // 加载导入的数据到表格
    await loadData(importedData)

    // 触发数据变化事件
    const currentData = getAllTableData()
    emit('dataChange', currentData)

    console.log(`成功导入 ${Object.keys(importedData).length} 个工作表`)

    // 清空文件输入框，允许重复导入同一文件
    if (fileInput.value) {
      fileInput.value.value = ''
    }

  } catch (error) {
    console.error('导入文件失败:', error)
    const errorMsg = formatErrorMessage('IMPORT_FAILED', error.message)
    emit('error', errorMsg)

    // 清空文件输入框
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  } finally {
    isLoading.value = false
  }
}

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData && Object.keys(newData).length > 0 && isInitialized.value) {
    loadData(newData)
  }
}, { deep: true, immediate: true })

// 高级导出功能
const exportDataWithOptions = async (options = {}) => {
  try {
    isLoading.value = true

    const {
      filename = `${props.workbookName}_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}`,
      includeSheets = null, // null表示导出所有工作表，数组表示指定工作表
      headerStyle = {
        font: { bold: true },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0F0F0' } },
        alignment: { horizontal: 'center', vertical: 'middle' }
      },
      addBorders = true,
      autoFitColumns = true,
      maxColumnWidth = 50,
      minColumnWidth = 10
    } = options

    const currentData = getAllTableData()

    if (!currentData || Object.keys(currentData).length === 0) {
      throw new Error('没有可导出的数据')
    }

    // 动态导入 exceljs
    const ExcelJS = await import('exceljs')
    const workbook = new ExcelJS.Workbook()

    // 设置工作簿属性
    workbook.creator = '通用表格系统'
    workbook.lastModifiedBy = '通用表格系统'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 筛选要导出的工作表
    const sheetsToExport = includeSheets
      ? Object.entries(currentData).filter(([name]) => includeSheets.includes(name))
      : Object.entries(currentData)

    // 为每个表格创建工作表
    sheetsToExport.forEach(([tableName, tableData]) => {
      if (!tableData || tableData.length === 0) return

      const worksheet = workbook.addWorksheet(tableName)

      // 添加数据
      tableData.forEach((row, rowIndex) => {
        const processedRow = row.map(cell => {
          // 处理 Univer 单元格对象格式
          if (cell && typeof cell === 'object' && 'v' in cell) {
            return cell.v
          }
          return cell
        })
        worksheet.addRow(processedRow)

        // 设置表头样式
        if (rowIndex === 0 && headerStyle) {
          const headerRow = worksheet.getRow(rowIndex + 1)
          if (headerStyle.font) headerRow.font = headerStyle.font
          if (headerStyle.fill) headerRow.fill = headerStyle.fill
          if (headerStyle.alignment) headerRow.alignment = headerStyle.alignment
        }
      })

      // 自动调整列宽
      if (autoFitColumns) {
        worksheet.columns.forEach((column, index) => {
          let maxLength = 0
          worksheet.eachRow((row) => {
            const cell = row.getCell(index + 1)
            const cellValue = cell.value ? String(cell.value) : ''
            maxLength = Math.max(maxLength, cellValue.length)
          })
          column.width = Math.min(Math.max(maxLength + 2, minColumnWidth), maxColumnWidth)
        })
      }

      // 添加边框
      if (addBorders && tableData.length > 0) {
        worksheet.eachRow((row, rowNumber) => {
          row.eachCell((cell, colNumber) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            }
          })
        })
      }
    })

    // 生成 Excel 文件并下载
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename.endsWith('.xlsx') ? filename : `${filename}.xlsx`
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理 URL 对象
    window.URL.revokeObjectURL(url)

    console.log(`成功导出文件: ${link.download}`)

    // 触发数据变化事件
    emit('dataChange', currentData)

    return {
      success: true,
      filename: link.download,
      sheetsCount: sheetsToExport.length
    }

  } catch (error) {
    console.error('导出数据失败:', error)
    const errorMsg = formatErrorMessage('EXPORT_FAILED', error.message)
    emit('error', errorMsg)
    return {
      success: false,
      error: errorMsg
    }
  } finally {
    isLoading.value = false
  }
}

// 暴露给父组件的方法
defineExpose({
  loadData,
  getAllTableData,
  refreshData,
  exportData,
  exportDataWithOptions
})

// 生命周期
onMounted(async () => {
  await initUniver()
  // 初始化完成后，watch 会自动处理 initialData 的加载
})

onBeforeUnmount(() => {
  if (univerInstance) {
    univerInstance.dispose()
  }
})
</script>

<style scoped>
.universal-table-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.floating-control-panel {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 20;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 8px;
  backdrop-filter: blur(4px);
}

.panel-header {
  display: flex;
  align-items: center;
}

.panel-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 20px;
}

.panel-controls {
  display: flex;
  gap: 8px;
}

.control-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 70px;
  justify-content: center;
}

.control-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.control-button.refresh {
  background: #4CAF50;
  color: white;
}

.control-button.refresh:hover:not(:disabled) {
  background: #45a049;
  transform: translateY(-1px);
}

.control-button.import {
  background: #FF9800;
  color: white;
}

.control-button.import:hover:not(:disabled) {
  background: #F57C00;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.control-button.export {
  background: #2196F3;
  color: white;
}

.control-button.export:hover:not(:disabled) {
  background: #1976D2;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.control-button.small {
  padding: 5px 10px;
  font-size: 12px;
  min-width: 65px;
}


.button-icon {
  font-size: 14px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.univer-container {
  flex: 1;
  position: relative;
  background: white;
  margin: 0;
  overflow: hidden;
}

.univer-container.loading {
  pointer-events: none;
}

.univer-spreadsheet {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-control-panel {
    top: 8px;
    right: 8px;
    padding: 6px;
  }

  .panel-controls {
    gap: 6px;
  }

  .control-button {
    min-width: 60px;
    padding: 4px 8px;
    font-size: 11px;
  }

  .button-icon {
    font-size: 12px;
  }
}
</style>