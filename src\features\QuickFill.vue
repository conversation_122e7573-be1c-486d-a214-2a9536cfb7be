<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        返回主页
      </el-button>
      <h1 class="title">速填精灵 - 快速数据填充</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <el-tooltip v-for="module in modules" :key="module.key" :content="module.tooltip" placement="bottom"
          :show-after="300" effect="light" :popper-style="{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '13px',
            padding: '8px 12px',
            maxWidth: '200px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }">
          <div :class="['nav-tab', { active: activeModule === module.key }]" @click="setActiveModule(module.key)">
            <el-icon>
              <component :is="module.icon" />
            </el-icon>
            <span>{{ module.title }}</span>
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import LaborDispatchClaim from '../components/LaborDispatchClaim.vue'
import ReverseFactoringApplication from '../components/ReverseFactoringApplication.vue'
import InvoiceQuickEntry from '../components/InvoiceQuickEntry.vue'
import SubcontractSettlement from '../components/SubcontractSettlement.vue'
import BatchTreasuryPayment from '../components/BatchTreasuryPayment.vue'

const router = useRouter()
const activeModule = ref('laborDispatch')

const modules = [
  {
    key: 'laborDispatch',
    title: '劳务派遣薪酬认领',
    icon: 'User',
    component: LaborDispatchClaim,
    tooltip: '快速处理劳务派遣人员的薪酬认领，支持批量操作和数据验证'
  },
  {
    key: 'reverseFactoring',
    title: '反向保理申请',
    icon: 'Document',
    component: ReverseFactoringApplication,
    tooltip: '处理反向保理业务申请'
  },
  {
    key: 'invoiceEntry',
    title: '发票速录',
    icon: 'Receipt',
    component: InvoiceQuickEntry,
    tooltip: '快速录入发票信息'
  },
  {
    key: 'subcontractSettlement',
    title: '分包结算速录',
    icon: 'Money',
    component: SubcontractSettlement,
    tooltip: '快速处理分包结算数据填报'
  },
  {
    key: 'batchTreasuryPayment',
    title: '批量司库付款单',
    icon: 'CreditCard',
    component: BatchTreasuryPayment,
    tooltip: '批量处理司库付款单据'
  }
]

const currentComponent = computed(() => {
  const module = modules.find(m => m.key === activeModule.value)
  return module ? module.component : 'div'
})

const setActiveModule = (key) => {
  activeModule.value = key
}

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.statistics-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  position: relative;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
  flex-shrink: 0;
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  overflow: hidden;
  background: #f5f7fa;
  height: 100%;
}

.content-area>* {
  height: 100%;
  overflow: auto;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.nav-tabs::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>