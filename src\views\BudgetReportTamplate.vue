<template>
  <div class="budget-report-template">
    <!-- 操作按钮行 -->
    <div class="action-buttons">
      <el-button type="primary" @click="getTemplateData" :loading="isLoadingTemplate" icon="Download">
        查询
      </el-button>
      <el-button type="success" @click="updateData" :loading="isUpdatingData" icon="Refresh">
        保存
      </el-button>
      <el-button type="info" @click="getSafetyFeeData" :loading="isLoadingSafetyFee" icon="Shield">
        安全费取数
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <UniversalTableComponent ref="tableRef" :initial-data="tableData" :data-provider="dataProvider"
        workbook-name="预算报表模板" @data-change="handleDataChange" @error="handleError"
        @initialized="handleTableInitialized" />
    </div>

    <!-- 消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import UniversalTableComponent from '@/components/UniversalTableComponent.vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

// 响应式数据
const tableRef = ref(null)
const tableData = ref({})
const dialogVisible = ref(false)

// 加载状态
const isLoadingTemplate = ref(false)
const isUpdatingData = ref(false)
const isLoadingSafetyFee = ref(false)

// 组件挂载时自动获取模板数据
onMounted(() => {
  getTemplateData()
})

// 获取模板数据
const getTemplateData = async () => {
  try {
    isLoadingTemplate.value = true

    const response = await fetch('http://127.0.0.1:8000/api/budget-report/template', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      // 直接赋值，让Vue的响应式系统处理
      tableData.value = result.data

      ElMessage.success('模板数据获取成功')
    } else {
      throw new Error(result.message || '获取模板数据失败')
    }
  } catch (error) {
    console.error('获取模板数据失败:', error)
    ElMessage.error('获取模板数据失败: ' + error.message)
  } finally {
    isLoadingTemplate.value = false
  }
}

// 更新数据
const updateData = async () => {
  try {
    isUpdatingData.value = true

    // 获取当前表格数据
    const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

    const response = await fetch('http://127.0.0.1:8000/api/budget-report/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        currentData: currentData,
        timestamp: new Date().toISOString()
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      // 更新表格数据
      if (result.data) {
        tableData.value = result.data
      }

      ElMessage.success('数据更新成功')
    } else {
      throw new Error(result.message || '数据更新失败')
    }
  } catch (error) {
    console.error('数据更新失败:', error)
    ElMessage.error('数据更新失败: ' + error.message)
  } finally {
    isUpdatingData.value = false
  }
}

// 安全费取数
const getSafetyFeeData = async () => {
  try {
    isLoadingSafetyFee.value = true

    // 获取当前表格数据
    const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

    const response = await fetch('http://127.0.0.1:8000/api/budget-report/safety-fee', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        currentData: currentData,
        timestamp: new Date().toISOString()
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      // 处理返回的二维数组数据
      if (result.data && Array.isArray(result.data)) {
        await writeSafetyFeeDataToTemplate(result.data)
      }

      ElMessage.success('安全费数据获取成功')
    } else {
      throw new Error(result.message || '安全费数据获取失败')
    }
  } catch (error) {
    console.error('安全费数据获取失败:', error)
    ElMessage.error('安全费数据获取失败: ' + error.message)
  } finally {
    isLoadingSafetyFee.value = false
  }
}

// 将安全费数据写入独立结账模板安全费表
const writeSafetyFeeDataToTemplate = async (data) => {
  if (!tableRef.value) {
    throw new Error('表格组件未初始化')
  }

  // 验证数据格式
  if (!data || data.length === 0) {
    throw new Error('安全费数据为空')
  }

  // 验证表头格式
  const expectedHeaders = ["利润中心组名称", "项目名称", "专项储备余额"]
  const actualHeaders = data[0]
  if (!actualHeaders || actualHeaders.length < 3) {
    throw new Error('数据格式错误：缺少必要的表头')
  }

  // 获取当前所有表格数据
  const currentData = tableRef.value.getAllTableData()

  // 检查是否存在独立结账模板安全费表
  const templateSheetName = '独立结账模板安全费'
  if (!currentData[templateSheetName]) {
    throw new Error(`未找到${templateSheetName}工作表`)
  }

  // 获取模板表的表头
  const templateData = currentData[templateSheetName]
  if (!templateData || templateData.length === 0) {
    throw new Error(`${templateSheetName}表格为空`)
  }

  const templateHeaders = templateData[0]
  const targetHeaders = [
    "序号", "组织机构", "项目名称", "事由", "专项储备计提比例",
    "专项储备余额", "专项储备基数", "安全费计提审批人1",
    "安全费计提审批人2", "安全费计提审批人3", "是否"
  ]

  // 验证模板表头是否正确
  const hasRequiredHeaders = targetHeaders.every(header =>
    templateHeaders.includes(header)
  )

  if (!hasRequiredHeaders) {
    console.warn('模板表头可能不完整，继续处理...')
  }

  // 找到列索引映射
  const getColumnIndex = (headers, columnName) => {
    const index = headers.findIndex(header => header === columnName)
    return index >= 0 ? index : -1
  }

  // 源数据列索引
  const sourceIndexes = {
    orgName: getColumnIndex(actualHeaders, "利润中心组名称"),
    projectName: getColumnIndex(actualHeaders, "项目名称"),
    reserveBalance: getColumnIndex(actualHeaders, "专项储备余额")
  }

  // 目标模板列索引
  const targetIndexes = {
    序号: getColumnIndex(templateHeaders, "序号"),
    组织机构: getColumnIndex(templateHeaders, "组织机构"),
    项目名称: getColumnIndex(templateHeaders, "项目名称"),
    事由: getColumnIndex(templateHeaders, "事由"),
    专项储备计提比例: getColumnIndex(templateHeaders, "专项储备计提比例"),
    专项储备余额: getColumnIndex(templateHeaders, "专项储备余额"),
    专项储备基数: getColumnIndex(templateHeaders, "专项储备基数"),
    安全费计提审批人1: getColumnIndex(templateHeaders, "安全费计提审批人1"),
    安全费计提审批人2: getColumnIndex(templateHeaders, "安全费计提审批人2"),
    安全费计提审批人3: getColumnIndex(templateHeaders, "安全费计提审批人3"),
    是否: getColumnIndex(templateHeaders, "是否")
  }

  // 先统计需要写入的有效数据行数
  let validDataCount = 0
  for (let i = 1; i < data.length; i++) {
    const sourceRow = data[i]
    const reserveBalance = sourceRow[sourceIndexes.reserveBalance]
    const reserveValue = Number(reserveBalance) || 0

    if (Math.abs(reserveValue) > 0.001) {
      validDataCount++
    }
  }

  if (validDataCount === 0) {
    ElMessage.warning('没有有效的安全费数据需要写入')
    return
  }

  // 准备新的模板数据，确保有足够的行
  const newTemplateData = [templateHeaders] // 保留表头

  // 计算需要的总行数（表头 + 有效数据行 + 额外空行）
  const requiredRows = 1 + validDataCount + 5 // 表头 + 数据 + 5行空行

  // 如果当前模板行数不够，先添加足够的空行
  while (newTemplateData.length < requiredRows) {
    const emptyRow = new Array(templateHeaders.length).fill('')
    newTemplateData.push(emptyRow)
  }

  // 处理数据行，跳过表头
  let sequenceNumber = 1
  let currentRowIndex = 1 // 从第二行开始写入数据（跳过表头）

  for (let i = 1; i < data.length; i++) {
    const sourceRow = data[i]

    // 检查专项储备余额是否有值且不为0
    const reserveBalance = sourceRow[sourceIndexes.reserveBalance]
    const reserveValue = Number(reserveBalance) || 0

    if (Math.abs(reserveValue) > 0.001) { // 只处理非零值
      // 确保有足够的行
      if (currentRowIndex >= newTemplateData.length) {
        const emptyRow = new Array(templateHeaders.length).fill('')
        newTemplateData.push(emptyRow)
      }

      // 获取当前行并填充数据
      const currentRow = newTemplateData[currentRowIndex]

      // 填充数据
      if (targetIndexes.序号 >= 0) {
        currentRow[targetIndexes.序号] = sequenceNumber++
      }

      if (targetIndexes.组织机构 >= 0 && sourceIndexes.orgName >= 0) {
        currentRow[targetIndexes.组织机构] = sourceRow[sourceIndexes.orgName] || ''
      }

      if (targetIndexes.项目名称 >= 0 && sourceIndexes.projectName >= 0) {
        currentRow[targetIndexes.项目名称] = sourceRow[sourceIndexes.projectName] || ''
      }

      if (targetIndexes.专项储备余额 >= 0) {
        currentRow[targetIndexes.专项储备余额] = reserveValue
      }

      // 设置默认值
      if (targetIndexes.事由 >= 0) {
        currentRow[targetIndexes.事由] = '安全费计提'
      }

      if (targetIndexes.是否 >= 0) {
        currentRow[targetIndexes.是否] = '是'
      }

      currentRowIndex++
    }
  }

  // 更新表格数据
  const updatedData = {
    ...currentData,
    [templateSheetName]: newTemplateData
  }

  // 重新加载数据到表格
  await tableRef.value.loadData(updatedData)

  console.log(`成功写入${validDataCount}行数据到${templateSheetName}，总行数：${newTemplateData.length}`)
}

// 数据提供函数（用于刷新）
const dataProvider = async () => {
  const response = await fetch('http://127.0.0.1:8000/api/budget-report/template', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const result = await response.json()

  if (result.code === 200) {
    return result.data
  } else {
    throw new Error(result.message || '获取数据失败')
  }
}

// 事件处理
const handleDataChange = (data) => {
  console.log('表格数据变化:', data)
}

const handleError = (error) => {
  console.error('表格错误:', error)
  ElMessage.error(error)
}

const handleTableInitialized = () => {
  console.log('表格初始化完成')
}
</script>

<style scoped>
.budget-report-template {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 60px);
  /* 减去顶部导航栏高度 */
  background: #f5f7fa;
}

.action-buttons {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.action-buttons .el-button {
  min-width: 120px;
}

.table-container {
  flex: 1;
  overflow: hidden;
  background: white;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-wrap: wrap;
    gap: 8px;
    padding: 12px 16px;
  }

  .action-buttons .el-button {
    flex: 1;
    min-width: auto;
  }
}
</style>