# 资金计划上传功能说明

## 功能概述

资金计划上传功能仿造DispatchSalaryComponent.vue的调用方式，提供了完整的资金计划数据管理功能，包括模板获取、数据计算和推送上传。

## 主要特性

### 1. 计划类型支持
- **正常计划**: 常规的资金计划申请
- **追加计划第一次**: 第一次追加的资金计划
- **追加计划第二次**: 第二次追加的资金计划

### 2. 核心功能
- **获取模板**: 获取标准的资金计划表格模板
- **计算补充**: 自动计算剩余金额等字段
- **推送数据**: 根据选择的计划类型推送数据到后端

### 3. 数据字段
- 项目编号
- 项目名称
- 预算科目
- 预算金额
- 已用金额
- 剩余金额（自动计算）
- 本次申请
- 申请原因
- 预计使用时间
- 负责人
- 备注

## 使用流程

### 1. 进入功能页面
在资金协管主页面中，点击"资金计划上传"标签页进入功能页面。

### 2. 获取模板
点击"获取模板"按钮，系统会加载标准的资金计划表格模板，包含示例数据。

### 3. 编辑数据
- 可以直接在表格中编辑数据
- 支持Excel导入功能
- 支持复制粘贴操作
- 支持筛选和排序

### 4. 计算补充
点击"计算补充"按钮，系统会自动计算：
- 剩余金额 = 预算金额 - 已用金额

### 5. 选择计划类型
在推送数据前，必须在下拉菜单中选择计划类型：
- 正常计划
- 追加计划第一次
- 追加计划第二次

### 6. 推送数据
选择计划类型后，点击"推送数据"按钮将数据上传到后端系统。

## 技术实现

### 前端组件
- `FundPlanUploadComponent.vue`: 主要的功能组件
- `FundPlanUploadView.vue`: 视图包装组件
- 集成到 `FundManager.vue` 中作为一个模块

### 后端API
- `GET /api/fund-plan/template`: 获取模板数据
- `POST /api/fund-plan/calculate`: 计算补充数据
- `POST /api/fund-plan/upload`: 上传资金计划数据
- `GET /api/fund-plan/types`: 获取计划类型列表
- `GET /api/fund-plan/history`: 获取上传历史记录

### 数据流程
1. 前端调用模板API获取初始数据
2. 用户编辑表格数据
3. 调用计算API进行数据补充
4. 选择计划类型并调用上传API推送数据
5. 后端记录上传日志并返回结果

## 界面特性

### 布局设计
- 采用与DispatchSalaryComponent相同的布局风格
- 顶部标题栏和返回按钮
- 中间功能按钮区域
- 底部表格展示区域

### 交互设计
- 推送数据按钮和计划类型下拉菜单并排显示
- 下拉菜单位于推送按钮左侧
- 必须选择计划类型才能启用推送按钮
- 加载状态和错误处理

### 响应式支持
- 表格尺寸根据窗口大小自动调整
- 支持窗口大小变化时的动态调整

## 错误处理

### 前端错误处理
- API调用失败时使用本地数据作为备选方案
- 用户操作验证和提示
- 加载状态管理

### 后端错误处理
- 参数验证
- 数据格式检查
- 异常捕获和错误返回

## 扩展性

### 计划类型扩展
可以通过修改 `planTypeOptions` 数组和后端API来添加新的计划类型。

### 字段扩展
可以通过修改模板数据结构来添加新的数据字段。

### 功能扩展
- 历史记录查询
- 数据导出功能
- 审批流程集成
- 权限控制

## 注意事项

1. 确保后端API服务正常运行
2. 计划类型必须选择才能推送数据
3. 数据格式需要符合模板要求
4. 建议在推送前进行数据验证