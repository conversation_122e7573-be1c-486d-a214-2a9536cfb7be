<template>
    <div class="fund-plan-upload">
        <!-- 操作按钮行 -->
        <div class="action-buttons">
            <el-button type="primary" @click="getTemplateData1" :loading="isLoadingTemplate1" icon="Download">
                初始化模板数据
            </el-button>
            <el-button type="primary" @click="getTemplateData2" :loading="isLoadingTemplate2" icon="Download">
                查询
            </el-button>
            <el-button type="warning" @click="saveTemplate" :loading="isSaving" icon="Document">
                保存
            </el-button>

            <!-- 计划类型选择 -->
            <el-select v-model="planType" placeholder="选择计划类型" style="width: 180px; margin-left: 12px;">
                <el-option v-for="option in planTypeOptions" :key="option.value" :label="option.label"
                    :value="option.value" />
            </el-select>

            <el-button type="success" @click="executeBatchUpload" :loading="isBatchUploading" icon="Upload"
                :disabled="!planType">
                执行批量上传
            </el-button>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
            <UniversalTableComponent ref="tableRef" :initial-data="tableData" :data-provider="dataProvider"
                workbook-name="资金计划上传" @data-change="handleDataChange" @error="handleError"
                @initialized="handleTableInitialized" />
        </div>

        <!-- 消息轮询对话框 -->
        <MessagePollingDialog v-model="dialogVisible" @dialog-closed="handleDialogClosed" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import UniversalTableComponent from './UniversalTableComponent.vue'
import MessagePollingDialog from './MessagePollingDialog.vue'

// 响应式数据
const tableRef = ref(null)
const tableData = ref({})
const dialogVisible = ref(false)
const planType = ref('')

// 加载状态
const isLoadingTemplate1 = ref(false)
const isLoadingTemplate2 = ref(false)
const isSaving = ref(false)
const isBatchUploading = ref(false)

// 计划类型选项
const planTypeOptions = [
    { label: '正常计划', value: 'normal' },
    { label: '追加计划第一次', value: 'additional_first' },
    { label: '追加计划第二次', value: 'additional_second' }
]

// 获取模板数据
const getTemplateData1 = async () => {
    try {
        isLoadingTemplate1.value = true

        const response = await fetch('http://127.0.0.1:8000/api/fund-plan/template1', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.code === 200) {
            // 直接赋值，让Vue的响应式系统处理
            tableData.value = result.data
            ElMessage.success('模板数据获取成功')
        } else {
            throw new Error(result.message || '获取模板数据失败')
        }
    } catch (error) {
        console.error('获取模板数据失败:', error)
        ElMessage.error('获取模板数据失败: ' + error.message)
    } finally {
        isLoadingTemplate1.value = false
    }
}
const getTemplateData2 = async () => {
    try {
        isLoadingTemplate2.value = true

        const response = await fetch('http://127.0.0.1:8000/api/fund-plan/template2', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.code === 200) {
            // 直接赋值，让Vue的响应式系统处理
            tableData.value = result.data
            ElMessage.success('模板数据获取成功')
        } else {
            throw new Error(result.message || '获取模板数据失败')
        }
    } catch (error) {
        console.error('获取模板数据失败:', error)
        ElMessage.error('获取模板数据失败: ' + error.message)
    } finally {
        isLoadingTemplate2.value = false
    }
}

// 保存模板
const saveTemplate = async () => {
    try {
        isSaving.value = true

        // 获取当前表格数据
        const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

        // 验证数据
        if (!currentData || Object.keys(currentData).length === 0) {
            ElMessage.warning('请先获取模板数据')
            return
        }

        const response = await fetch('http://127.0.0.1:8000/api/fund-plan/save-template', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                templateData: currentData,
                timestamp: new Date().toISOString()
            })
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.code === 200) {
            ElMessage.success('模板保存成功')
        } else {
            throw new Error(result.message || '模板保存失败')
        }
    } catch (error) {
        console.error('保存模板失败:', error)
        ElMessage.error('保存模板失败: ' + error.message)
    } finally {
        isSaving.value = false
    }
}

// 执行批量上传
const executeBatchUpload = async () => {
    try {
        isBatchUploading.value = true

        // 验证计划类型
        if (!planType.value) {
            ElMessage.warning('请选择计划类型')
            return
        }

        // 获取当前表格数据
        const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

        // 验证数据
        if (!currentData || Object.keys(currentData).length === 0) {
            ElMessage.warning('请先获取模板数据')
            return
        }

        const response = await fetch('http://127.0.0.1:8000/api/start-function', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                '功能': '资金计划批量上传',
                '参数': [planType.value]
            })
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (response.ok) {
            // 显示消息轮询对话框
            dialogVisible.value = true
            ElMessage.success(`批量上传任务已启动 (计划类型: ${planTypeOptions.find(opt => opt.value === planType.value)?.label})`)
        } else {
            throw new Error(result.message || '批量上传启动失败')
        }
    } catch (error) {
        console.error('批量上传失败:', error)
        ElMessage.error('批量上传失败: ' + error.message)
    } finally {
        isBatchUploading.value = false
    }
}

// 数据提供函数（用于刷新）
const dataProvider = async () => {
    const response = await fetch('http://127.0.0.1:8000/api/fund-plan/template', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
        return result.data
    } else {
        throw new Error(result.message || '获取数据失败')
    }
}

// 事件处理
const handleDataChange = (data) => {
    console.log('表格数据变化:', data)
}

const handleError = (error) => {
    console.error('表格错误:', error)
    ElMessage.error(error)
}

const handleTableInitialized = () => {
    console.log('表格初始化完成')
}

const handleDialogClosed = () => {
    console.log('对话框已关闭，重新获取模板数据')
    getTemplateData2()
}

// 组件挂载后自动执行getTemplateData2
onMounted(() => {
    getTemplateData2()
})
</script>

<style scoped>
.fund-plan-upload {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #f5f7fa;
    overflow: hidden;
}

.action-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.action-buttons .el-button {
    min-width: 120px;
}

.table-container {
    flex: 1;
    overflow: hidden;
    background: white;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .action-buttons {
        flex-wrap: wrap;
        gap: 8px;
        padding: 12px 16px;
    }

    .action-buttons .el-button {
        flex: 1;
        min-width: auto;
    }

    .action-buttons .el-select {
        width: 100% !important;
        margin-left: 0 !important;
        margin-top: 8px;
    }
}
</style>