<template>
  <div class="sap-export-container">
    <!-- 主数据导出 -->
    <div class="export-section">
      <div class="description-panel">
        <h3>主数据导出</h3>
        <p>导出SAP系统中的主数据项目信息</p>
        <ul>
          <li>包含所有项目</li>
          <li>成本中心信息</li>
          <li>用于内外部客商识别，及组织机构获取</li>
        </ul>
      </div>
      <div class="export-form">
        <h2>主数据导出</h2>
        <el-form label-width="120px">
          <el-form-item>
            <el-button type="primary" @click="exportMasterData" :loading="masterDataLoading">
              {{ masterDataLoading ? '导出中...' : '导出主数据' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- SAP数据导出 -->
    <div class="export-section">
      <div class="description-panel">
        <h3>SAP明细数据导出</h3>
        <p>导出指定日期范围内的SAP会计凭证综合查询的明细账数据</p>
        <ul>
          <li>用于生成项目子报表</li>
          <li>可用于科目余额表替代性生成,用于结账</li>
          <li>支持自定义日期范围</li>
          <li>包含完整的业务凭证信息</li>
        </ul>
      </div>
      <div class="export-form">
        <h2>SAP数据导出</h2>
        <el-form :model="form" label-width="120px">
          <el-form-item label="开始日期">
            <el-date-picker v-model="form.startDate" type="date" placeholder="选择开始日期" value-format="YYYY-MM-DD"
              format="YYYY年MM月DD日" />
          </el-form-item>

          <el-form-item label="结束日期">
            <el-date-picker v-model="form.endDate" type="date" placeholder="选择结束日期" value-format="YYYY-MM-DD"
              format="YYYY年MM月DD日" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="exportData" :loading="loading">
              {{ loading ? '导出中...' : '导出数据' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- SAP科目余额表导出 -->
    <div class="export-section">
      <div class="description-panel">
        <h3>SAP科目余额表导出</h3>
        <p>导出指定时间段内的SAP科目余额表数据支持本年和往年数据的分别存储和管理。</p>
        <ul>
          <li>用于结账分析</li>
          <li>用于稽核</li>
          <li>用于数据汇总查询</li>
          <li>完整的科目余额汇总信息</li>
        </ul>
      </div>
      <div class="export-form">
        <h2>SAP科目余额表导出</h2>
        <el-form :model="balanceForm" label-width="120px">
          <el-form-item label="存储位置">
            <el-select v-model="balanceForm.storageLocation" placeholder="请选择存储位置">
              <el-option label="本年科目余额表" value="current_year" />
              <el-option label="往年科目余额表" value="previous_year" />
            </el-select>
          </el-form-item>

          <el-form-item label="开始日期">
            <el-date-picker v-model="balanceForm.startDate" type="date" placeholder="选择开始日期" value-format="YYYY-MM-DD"
              format="YYYY年MM月DD日" />
          </el-form-item>

          <el-form-item label="结束日期">
            <el-date-picker v-model="balanceForm.endDate" type="date" placeholder="选择结束日期" value-format="YYYY-MM-DD"
              format="YYYY年MM月DD日" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="exportBalanceData" :loading="balanceLoading">
              {{ balanceLoading ? '导出中...' : '导出科目余额表' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 内部对账导出 -->
    <div class="export-section">
      <div class="description-panel">
        <h3>内部对账导出</h3>
        <p>导出内部对账相关数据及数据管理功能</p>
        <ul>
          <li>用于与总包上级项目的内部往来核对</li>
          <li>来源于sap内部对账</li>
          <li>与sap明细数据构建的数据集进行比对</li>
          <li>另外可用于抵消检查</li>
          <li>将已导出的数据导入到数据库中</li>
          <li>继续导出之前未成功导出的数据部分</li>
        </ul>
      </div>
      <div class="export-form">
        <h2>内部对账导出</h2>
        <el-form label-width="120px">
          <el-form-item>
            <el-button type="primary" @click="exportInternalReconciliation" :loading="reconciliationLoading">
              {{ reconciliationLoading ? '导出中...' : '导出内部对账' }}
            </el-button>
          </el-form-item>
          
          <el-form-item>
            <el-button type="success" @click="importExportedData" :loading="importLoading">
              {{ importLoading ? '导入中...' : '已导出数据导入数据库' }}
            </el-button>
          </el-form-item>
          
          <el-form-item>
            <el-button type="warning" @click="continueFailedExport" :loading="continueLoading">
              {{ continueLoading ? '继续导出中...' : '未成功部分继续导出' }}
            </el-button>
          </el-form-item>

          <el-form-item>
            <el-button type="danger" @click="quickGenerateReconciliation" :loading="quickReconLoading">
              {{ quickReconLoading ? '生成中...' : '快速生成收付款对账文件' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- SAP累计过账导出 -->
    <div class="export-section">
      <div class="description-panel">
        <h3>SAP累计过账导出</h3>
        <p>导出指定年月的SAP累计过账数据</p>
        <ul>
          <li>用于校正收入成本呢</li>
          <li>支持自定义年份和月份</li>
          <li>从sap成本测算查询导出</li>
        </ul>
      </div>
      <div class="export-form">
        <h2>SAP累计过账导出</h2>
        <el-form :model="cumulativeForm" label-width="120px">
          <el-form-item label="年份">
            <el-input-number v-model="cumulativeForm.year" :min="2000" :max="2100" placeholder="选择年份" />
          </el-form-item>

          <el-form-item label="月份">
            <el-input-number v-model="cumulativeForm.month" :min="1" :max="12" placeholder="选择月份" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="exportCumulativeData" :loading="cumulativeLoading">
              {{ cumulativeLoading ? '导出中...' : '导出累计过账' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const form = ref({
  startDate: '',
  endDate: ''
})

const balanceForm = ref({
  storageLocation: '',
  startDate: '',
  endDate: ''
})

const cumulativeForm = ref({
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1
})

const loading = ref(false)
const masterDataLoading = ref(false)
const balanceLoading = ref(false)
const reconciliationLoading = ref(false)
const cumulativeLoading = ref(false)
const importLoading = ref(false)
const continueLoading = ref(false)
const quickReconLoading = ref(false)
const dialogVisible = ref(false)

// 获取默认日期
const getDefaultDate = async () => {
  try {
    const response = await fetch('http://127.0.0.1:8000/api/get-default-date', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      // 为所有日期字段设置默认值
      form.value.startDate = data.startDate1
      form.value.endDate = data.endDate1
      balanceForm.value.startDate = data.startDate2
      balanceForm.value.endDate = data.endDate2
    }
  } catch (error) {
    console.error('获取默认日期失败:', error)
  }
}

// 主数据导出
const exportMasterData = async () => {
  masterDataLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '导出主数据',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('主数据导出任务已启动')
    } else {
      ElMessage.error('主数据导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    masterDataLoading.value = false
  }
}

// SAP数据导出
const exportData = async () => {
  if (!form.value.startDate || !form.value.endDate) {
    ElMessage.warning('请选择开始日期和结束日期')
    return
  }

  if (form.value.startDate > form.value.endDate) {
    ElMessage.warning('开始日期不能晚于结束日期')
    return
  }

  loading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': 'sap明细数据导出',
        '参数': [form.value.startDate, form.value.endDate]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('数据导出任务已启动')
    } else {
      ElMessage.error('数据导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    loading.value = false
  }
}

// SAP科目余额表导出
const exportBalanceData = async () => {
  if (!balanceForm.value.storageLocation) {
    ElMessage.warning('请选择存储位置')
    return
  }

  if (!balanceForm.value.startDate || !balanceForm.value.endDate) {
    ElMessage.warning('请选择开始日期和结束日期')
    return
  }

  if (balanceForm.value.startDate > balanceForm.value.endDate) {
    ElMessage.warning('开始日期不能晚于结束日期')
    return
  }

  balanceLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': 'sap科目余额表导出',
        '参数': [balanceForm.value.storageLocation, balanceForm.value.startDate, balanceForm.value.endDate]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('科目余额表导出任务已启动')
    } else {
      ElMessage.error('科目余额表导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    balanceLoading.value = false
  }
}

// 内部对账导出
const exportInternalReconciliation = async () => {
  reconciliationLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '导出内部对账',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('内部对账导出任务已启动')
    } else {
      ElMessage.error('内部对账导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    reconciliationLoading.value = false
  }
}

// SAP累计过账导出
const exportCumulativeData = async () => {
  if (!cumulativeForm.value.year || !cumulativeForm.value.month) {
    ElMessage.warning('请选择年份和月份')
    return
  }

  cumulativeLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '累计过账数据获取',
        '参数': [parseInt(cumulativeForm.value.year), parseInt(cumulativeForm.value.month)]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('累计过账导出任务已启动')
    } else {
      ElMessage.error('累计过账导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    cumulativeLoading.value = false
  }
}

// 已导出数据导入数据库
const importExportedData = async () => {
  importLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '内部对账已导出数据导入数据库',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('数据导入任务已启动')
    } else {
      ElMessage.error('数据导入失败')
    }
  } catch (error) {
    ElMessage.error('导入过程中发生错误: ' + error.message)
  } finally {
    importLoading.value = false
  }
}

// 未成功部分继续导出
const continueFailedExport = async () => {
  continueLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '内部对账未成功部分继续导出',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('继续导出任务已启动')
    } else {
      ElMessage.error('继续导出失败')
    }
  } catch (error) {
    ElMessage.error('继续导出过程中发生错误: ' + error.message)
  } finally {
    continueLoading.value = false
  }
}

// 快速生成收付款对账文件
const quickGenerateReconciliation = async () => {
  quickReconLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/internal-reconciliation', {
      method: 'POST',
      headers: {
        'Accept': '*/*'
      }
    })

    if (!response.ok) {
      let message = '生成失败'
      try {
        const text = await response.text()
        message = text || message
      } catch (e) {
        // ignore
      }
      ElMessage.error(message)
      return
    }{ElMessage.success("成功导出文件，文件名2021年之后内部对账")}
  } finally {
    quickReconLoading.value = false
  }
}

// 组件挂载时获取默认日期
onMounted(() => {
  getDefaultDate()
})
</script>

<style scoped>
.sap-export-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
  position: relative;
}

.export-section {
  display: flex;
  gap: 20px;
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
  align-items: flex-start;
}

.export-section:last-of-type {
  margin-bottom: 0;
}

.description-panel {
  flex: 1;
  padding-right: 20px;
  border-right: 1px solid #e4e7ed;
}

.description-panel h3 {
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  margin-top: 0;
}

.description-panel p {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
  text-align: justify;
}

.description-panel ul {
  color: #606266;
  font-size: 13px;
  margin: 0;
  padding-left: 20px;
}

.description-panel li {
  margin-bottom: 5px;
  line-height: 1.4;
}

.export-form {
  flex: 1;
  max-width: 400px;
  padding: 0;
  border: none;
  background: transparent;
  margin: 0;
}

.export-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .export-section {
    flex-direction: column;
    gap: 15px;
  }
  
  .description-panel {
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
    padding-right: 0;
    padding-bottom: 15px;
  }
  
  .export-form {
    max-width: 100%;
  }
}
</style>