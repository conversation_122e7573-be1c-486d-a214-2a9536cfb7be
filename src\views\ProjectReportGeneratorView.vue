<template>
  <div class="project-report-container">
    <div class="report-header">
      <h2>项目报表生成器</h2>
      <div class="export-button-container">
        <el-button 
          type="primary" 
          @click="exportProjectReport" 
          :loading="loading"
          class="export-button"
        >
          <el-icon><Download /></el-icon>
          {{ loading ? '导出中...' : '导出项目报表' }}
        </el-button>
      </div>
    </div>

    <div class="report-content">
      <p class="description">
        点击上方按钮导出项目报表数据，系统将自动生成完整的项目报表。
      </p>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const loading = ref(false)
const dialogVisible = ref(false)

const exportProjectReport = async () => {
  loading.value = true

  try {
    // 发送POST请求导出数据
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '生成项目报表',
        '参数': []
      })
    })

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('项目报表导出任务已启动')
    } else {
      ElMessage.error('项目报表导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.project-report-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
  position: relative;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.report-header h2 {
  color: #303133;
  margin: 0;
}

.export-button-container {
  display: flex;
  justify-content: center;
}

.export-button {
  font-weight: 600;
  padding: 12px 20px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.export-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.report-content {
  padding: 20px 0;
}

.description {
  color: #606266;
  font-size: 16px;
  line-height: 1.6;
  text-align: center;
  margin-top: 40px;
}
</style>