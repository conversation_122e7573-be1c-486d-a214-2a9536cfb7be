<template>
  <div class="international-business">
    <div class="page-header">
      <h2 class="page-title">国际业务</h2>
      <p class="page-description">管理国际业务相关的银行流水模板和数据处理</p>
    </div>

    <!-- 流水导入部分 -->
    <div class="import-section">
      <div class="section-header">
        <el-icon class="section-icon"><Upload /></el-icon>
        <h3 class="section-title">流水转写</h3>
      </div>
      
      <div class="import-controls">
        <div class="control-group">
          <label class="control-label">银行流水模板：</label>
          <el-select
            v-model="selectedTemplate"
            placeholder="请选择银行流水模板"
            class="template-select"
            clearable
          >
            <el-option
              v-for="template in bankTemplates"
              :key="template.value"
              :label="template.label"
              :value="template.value"
            />
          </el-select>
        </div>
        
        <div class="control-group">
          <el-button
            type="primary"
            :icon="DocumentAdd"
            @click="generateTemplate"
            :disabled="!selectedTemplate"
            :loading="loading"
            class="generate-btn"
          >
            流水模板生成
          </el-button>
        </div>
      </div>

      <!-- 模板预览区域 -->
      <div v-if="templatePreview" class="template-preview">
        <div class="preview-header">
          <h4>模板预览</h4>
          <el-button size="small" @click="downloadTemplate" :icon="Download">
            下载模板
          </el-button>
        </div>
        <div class="preview-content">
          <el-table :data="templatePreview.data" border size="small">
            <el-table-column
              v-for="column in templatePreview.columns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            />
          </el-table>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Upload,
  DocumentAdd,
  Download
} from '@element-plus/icons-vue'
import axios from 'axios'

// 响应式数据
const selectedTemplate = ref('')
const templatePreview = ref(null)
const loading = ref(false)

// 银行流水模板选项
const bankTemplates = reactive([])

// 获取银行模板列表
const fetchBankTemplates = async () => {
  try {
    // 尝试从后台获取模板列表
    const response = await axios.get('/api/bank-templates')
    bankTemplates.splice(0, bankTemplates.length, ...response.data)
  } catch (error) {
    console.warn('获取模板列表失败，使用模拟数据:', error)
    // 获取失败时使用模拟数据
    const mockTemplates = [
      { value: 'icbc', label: '菲律宾流水模板' },
      { value: 'abc', label: '农业银行流水模板' },
      { value: 'boc', label: '中国银行流水模板' },
      { value: 'ccb', label: '建设银行流水模板' },
      { value: 'cmb', label: '招商银行流水模板' },
      { value: 'spdb', label: '浦发银行流水模板' },
      { value: 'citic', label: '中信银行流水模板' },
      { value: 'standard', label: '标准流水模板' }
    ]
    bankTemplates.splice(0, bankTemplates.length, ...mockTemplates)
  }
}

// 组件挂载时获取模板列表
onMounted(() => {
  fetchBankTemplates()
})

// 生成流水模板
const generateTemplate = async () => {
  if (!selectedTemplate.value) {
    ElMessage.warning('请先选择银行流水模板')
    return
  }

  loading.value = true
  try {
    ElMessage.info('正在生成模板...')

    // 向后台发送请求生成模板
    const response = await axios.post('/api/generate-template', {
      templateType: selectedTemplate.value
    })

    templatePreview.value = response.data
    ElMessage.success('模板生成成功')
  } catch (error) {
    console.error('生成模板失败:', error)
    ElMessage.error('生成模板失败，请稍后重试')

    // 请求失败时使用模拟数据
    const mockTemplateData = generateMockTemplateData()
    templatePreview.value = mockTemplateData
    ElMessage.info('已使用模拟数据生成模板')
  } finally {
    loading.value = false
  }
}

// 生成模拟模板数据（作为后备方案）
const generateMockTemplateData = () => {
  const baseColumns = [
    { prop: 'date', label: '交易日期', width: 120 },
    { prop: 'time', label: '交易时间', width: 100 },
    { prop: 'amount', label: '交易金额', width: 120 },
    { prop: 'balance', label: '余额', width: 120 },
    { prop: 'currency', label: '币种', width: 80 },
    { prop: 'counterparty', label: '对方账户', width: 150 },
    { prop: 'purpose', label: '用途', width: 200 },
    { prop: 'reference', label: '参考号', width: 150 }
  ]

  const sampleData = [
    {
      date: '2024-01-01',
      time: '09:30:00',
      amount: '10000.00',
      balance: '50000.00',
      currency: 'USD',
      counterparty: '1234567890',
      purpose: '货款',
      reference: 'REF001'
    }
  ]

  return {
    columns: baseColumns,
    data: sampleData
  }
}

// 下载模板
const downloadTemplate = () => {
  if (!templatePreview.value) {
    ElMessage.warning('请先生成模板')
    return
  }

  ElMessage.info('开始下载模板...')
  // 这里实现模板下载逻辑
  // 可以将模板数据转换为Excel文件并下载
}
</script>

<style scoped>
.international-business {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100%;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
  font-size: 14px;
}

.import-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e8f4fd;
}

.section-icon {
  font-size: 20px;
  color: #1976d2;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.import-controls {
  display: flex;
  gap: 24px;
  align-items: end;
  margin-bottom: 24px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label {
  font-size: 14px;
  font-weight: 500;
  color: #5a6c7d;
}

.template-select {
  width: 240px;
}

.generate-btn {
  height: 40px;
  padding: 0 20px;
}

.template-preview {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
}

.preview-header h4 {
  margin: 0;
  font-size: 16px;
  color: #2c3e50;
}

.preview-content {
  padding: 16px;
}

.data-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.file-size {
  color: #7f8c8d;
  font-size: 12px;
}
</style>
