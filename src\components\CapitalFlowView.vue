<template>
  <div class="capital-flow-view">
    <!-- 操作按钮行 -->
    <div class="action-buttons">
      <!-- 初始化台账区域 -->
      <div class="init-section">
        <el-date-picker v-model="startDate" type="date" placeholder="开始日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
          style="width: 140px; margin-right: 8px;" />
        <el-date-picker v-model="endDate" type="date" placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
          style="width: 140px; margin-right: 12px;" />
        <el-button type="primary" @click="initializeAccount" :loading="isInitializing" icon="Plus">
          初始化台账
        </el-button>
      </div>

      <!-- 司库资金余额区域 -->
      <div class="export-section">
        <el-date-picker v-model="exportStartDate" type="date" placeholder="导出开始日期" format="YYYY-MM-DD"
          value-format="YYYY-MM-DD" style="width: 140px; margin-right: 8px;" />
        <el-date-picker v-model="exportEndDate" type="date" placeholder="导出结束日期" format="YYYY-MM-DD"
          value-format="YYYY-MM-DD" style="width: 140px; margin-right: 12px;" />
        <el-button type="info" @click="exportTreasuryPayment" :loading="isExporting" icon="Download">
          司库资金余额导出
        </el-button>
      </div>

      <!-- 其他操作按钮 -->
      <el-button type="success" @click="getTemplateData" :loading="isLoadingTemplate" icon="Download">
        查询
      </el-button>
      <el-button type="warning" @click="updateData" :loading="isUpdatingData" icon="Refresh">
        保存
      </el-button>
      <el-button type="danger" @click="executeCapitalFlow" :loading="isExecuting" icon="Check">
        财商收支台账批量填入
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <UniversalTableComponent ref="tableRef" :initial-data="tableData" :data-provider="dataProvider"
        workbook-name="财商收支台账" @data-change="handleDataChange" @error="handleError"
        @initialized="handleTableInitialized" />
    </div>

    <!-- 消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" @dialog-closed="handleDialogClosed" />
  </div>
</template>

<script setup>
import { ref,onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import UniversalTableComponent from './UniversalTableComponent.vue'
import MessagePollingDialog from './MessagePollingDialog.vue'

// 响应式数据
const tableRef = ref(null)
const tableData = ref({})
const dialogVisible = ref(false)

// 日期选择
const startDate = ref('')
const endDate = ref('')

// 司库在途支付导出日期选择
const exportStartDate = ref('')
const exportEndDate = ref('')

// 加载状态
const isInitializing = ref(false)
const isLoadingTemplate = ref(false)
const isUpdatingData = ref(false)
const isExecuting = ref(false)
const isExporting = ref(false)

// 初始化台账
const initializeAccount = async () => {
  if (!startDate.value || !endDate.value) {
    ElMessage.warning('请选择开始日期和结束日期')
    return
  }

  if (new Date(startDate.value) > new Date(endDate.value)) {
    ElMessage.warning('开始日期不能晚于结束日期')
    return
  }

  try {
    isInitializing.value = true

    const response = await fetch('http://127.0.0.1:8000/api/moneyIO-flow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        start_date: startDate.value,
        end_date: endDate.value
      })
    })

    if (!response.ok) {
      // 尝试解析错误响应
      let errorMessage = `HTTP error! status: ${response.status}`
      try {
        const errorData = await response.json()
        if (errorData.detail) {
          errorMessage = errorData.detail
        }
      } catch (parseError) {
        console.warn('无法解析错误响应:', parseError)
      }
      throw new Error(errorMessage)
    }

    const result = await response.json()

    // 将API返回的数据转换为表格格式
    const formattedData = {
      '财商收支台账': result,
    }

    // 直接赋值，让Vue的响应式系统处理
    tableData.value = formattedData

    ElMessage.success('台账初始化成功')
  } catch (error) {
    console.error('初始化台账失败:', error)
    ElMessage.error('初始化台账失败: ' + error.message)
  } finally {
    isInitializing.value = false
  }
}

// 获取模板数据
const getTemplateData = async () => {
  try {
    isLoadingTemplate.value = true

    // 使用默认日期范围获取模板数据
    const defaultStartDate = new Date()
    defaultStartDate.setMonth(defaultStartDate.getMonth() - 1)
    const defaultEndDate = new Date()

    const response = await fetch('http://127.0.0.1:8000/api/moneyIO-flow2', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        start_date: defaultStartDate.toISOString().split('T')[0],
        end_date: defaultEndDate.toISOString().split('T')[0]
      })
    })

    if (!response.ok) {
      // 尝试解析错误响应
      let errorMessage = `HTTP error! status: ${response.status}`
      try {
        const errorData = await response.json()
        if (errorData.detail) {
          errorMessage = errorData.detail
        }
      } catch (parseError) {
        console.warn('无法解析错误响应:', parseError)
      }
      throw new Error(errorMessage)
    }

    const result = await response.json()

    // 将API返回的数据转换为表格格式
    const formattedData = result

    // 直接赋值，让Vue的响应式系统处理
    tableData.value = formattedData

    ElMessage.success('模板数据获取成功')
  } catch (error) {
    console.error('获取模板数据失败:', error)
    ElMessage.error('获取模板数据失败: ' + error.message)
  } finally {
    isLoadingTemplate.value = false
  }
}

// 更新数据
const updateData = async () => {
  try {
    isUpdatingData.value = true

    // 获取当前表格数据
    const TablecurrentData = tableRef.value ? tableRef.value.getAllTableData() : {}

    const response = await fetch('http://127.0.0.1:8000/api/moneyIO-flow/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        currentData: TablecurrentData
      })
    })

    if (!response.ok) {
      // 尝试解析错误响应
      let errorMessage = `HTTP error! status: ${response.status}`
      try {
        const errorData = await response.json()
        if (errorData.detail) {
          errorMessage = errorData.detail
        }
      } catch (parseError) {
        console.warn('无法解析错误响应:', parseError)
      }
      throw new Error(errorMessage)
    } else {
      ElMessage.success('数据更新成功')
    }
  } catch (error) {
    console.error('数据更新失败:', error)
    ElMessage.error('数据更新失败: ' + error.message)
  } finally {
    isUpdatingData.value = false
  }
}

// 执行资金流处理
const executeCapitalFlow = async () => {
  try {
    isExecuting.value = true

    // 获取当前表格数据
    const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

    // 验证数据
    if (!currentData || Object.keys(currentData).length === 0) {
      ElMessage.warning('请先初始化台账或获取模板数据')
      return
    }

    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '上传收支台账',
        '参数': []
      })
    })

    if (!response.ok) {
      // 尝试解析错误响应
      let errorMessage = `HTTP error! status: ${response.status}`
      try {
        const errorData = await response.json()
        if (errorData.detail) {
          errorMessage = errorData.detail
        }
      } catch (parseError) {
        console.warn('无法解析错误响应:', parseError)
      }
      throw new Error(errorMessage)
    }

    const result = await response.json()

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('资金流处理任务已启动')
    } else {
      throw new Error(result.message || '资金流处理启动失败')
    }
  } catch (error) {
    console.error('资金流处理失败:', error)
    ElMessage.error('资金流处理失败: ' + error.message)
  } finally {
    isExecuting.value = false
  }
}

// 表格数据补全功能
const fillTableData = async (data) => {
  try {
    // 验证数据格式
    if (!Array.isArray(data) || data.length === 0) {
      throw new Error('数据格式无效，需要二维数组')
    }

    // 获取当前财商收支台账表格数据
    const currentTableData = tableRef.value ? tableRef.value.getAllTableData() : {}
    const capitalFlowData = currentTableData['财商收支台账']

    if (!capitalFlowData || !Array.isArray(capitalFlowData) || capitalFlowData.length === 0) {
      throw new Error('请先初始化财商收支台账表格')
    }

    // 构建字典：使用data的第5列和第17列（索引4和16）
    const dictionary = {}
    data.forEach(row => {
      if (row.length > 16) { // 确保行有足够的列
        const key = row[4] // 第5列作为key
        const value = row[16] // 第17列作为value
        if (key && value) {
          dictionary[key] = value
        }
      }
    })

    console.log('构建的字典:', dictionary)

    // 补全表格数据
    const updatedData = capitalFlowData.map((row, rowIndex) => {
      if (rowIndex === 0) return row // 跳过表头行

      const newRow = [...row]

      // 确保行有足够的列
      while (newRow.length < 12) {
        newRow.push('')
      }

      // 获取第1列的值，转为文本并前面加上"NH"
      const firstColumnValue = row[0]
      if (firstColumnValue) {
        const searchKey = 'NH' + String(firstColumnValue)

        // 在字典中查找匹配的值
        if (dictionary[searchKey]) {
          newRow[11] = dictionary[searchKey] // 第12列（索引11）
          console.log(`匹配成功: ${searchKey} -> ${dictionary[searchKey]}`)
        } else {
          console.log(`未找到匹配: ${searchKey}`)
        }
      }

      return newRow
    })

    // 更新表格数据
    const updatedTableData = {
      ...currentTableData,
      '财商收支台账': updatedData
    }

    // 更新表格显示
    tableData.value = updatedTableData

    console.log('表格数据补全完成')
  } catch (error) {
    console.error('表格数据补全失败:', error)
    ElMessage.error('表格数据补全失败: ' + error.message)
  }
}

const exportTreasuryPayment = async () => {
  if (!exportStartDate.value || !exportEndDate.value) {
    ElMessage.warning('请选择导出开始日期和结束日期')
    return
  }

  if (new Date(exportStartDate.value) > new Date(exportEndDate.value)) {
    ElMessage.warning('开始日期不能晚于结束日期')
    return
  }

  try {
    isExporting.value = true

    const response = await fetch('http://127.0.0.1:8000/api/fip-balance-export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        start_date: exportStartDate.value,
        end_date: exportEndDate.value
      })
    })

    if (!response.ok) {
      // 尝试解析错误响应
      let errorMessage = `HTTP error! status: ${response.status}`
      try {
        const errorData = await response.json()
        if (errorData.detail) {
          errorMessage = errorData.detail
        }
      } catch (parseError) {
        console.warn('无法解析错误响应:', parseError)
      }
      throw new Error(errorMessage)
    }

    const data = await response.json()

    // 调用表格补全功能
    await fillTableData(data)

    ElMessage.success('司库余额导出成功')

  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  } finally {
    isExporting.value = false
  }
}

// 数据提供者函数
const dataProvider = async () => {
  // 这里可以实现数据获取逻辑
  return tableData.value
}

// 处理数据变化
const handleDataChange = (data) => {
  console.log('表格数据发生变化:', data)
  // 可以在这里添加数据变化的处理逻辑
}

// 处理错误
const handleError = (error) => {
  console.error('表格组件错误:', error)
  ElMessage.error('表格操作失败: ' + error)
}

// 处理表格初始化完成
const handleTableInitialized = () => {
  console.log('表格初始化完成')
}

const handleDialogClosed = () => {
    console.log('对话框已关闭，重新获取模板数据')
    getTemplateData()
}

// 组件挂载后自动执行getTemplateData
onMounted(() => {
    getTemplateData()
})
</script>

<style scoped>
.capital-flow-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  background: #f5f7fa;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  flex-wrap: wrap;
  z-index: 10;
}

.init-section {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-right: 12px;
}

.export-section {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #f0f8ff;
  border-radius: 6px;
  border: 1px solid #d1ecf1;
  margin-right: 12px;
}

.action-buttons .el-button {
  min-width: 120px;
}

.table-container {
  flex: 1;
  min-height: 0;
  background: white;
  margin: 0;
  position: relative;
}

/* 确保Univer组件能够正确显示 */
.table-container :deep(.univer-container) {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.table-container :deep(.univer-spreadsheet) {
  height: 100% !important;
  flex: 1 !important;
}

/* 确保Univer底栏显示 */
.table-container :deep(.univer-footer) {
  display: block !important;
  height: auto !important;
}

.table-container :deep(.univer-status-bar) {
  display: flex !important;
  height: auto !important;
}

/* 修复可能的overflow问题 */
.table-container :deep(.univer-workbook) {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.table-container :deep(.univer-sheet-container) {
  flex: 1 !important;
  min-height: 0 !important;
  overflow: auto !important;
}

/* 确保所有Univer相关的元素都能正确显示 */
.table-container :deep(.univer) {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.table-container :deep(.univer-render-engine) {
  flex: 1 !important;
  min-height: 0 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .action-buttons {
    flex-wrap: wrap;
    gap: 8px;
    padding: 12px 16px;
  }

  .init-section,
  .export-section {
    width: 100%;
    margin-right: 0;
    margin-bottom: 8px;
    justify-content: center;
  }

  .action-buttons .el-button {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 768px) {

  .init-section,
  .export-section {
    flex-direction: column;
    gap: 8px;
  }

  .init-section .el-date-picker,
  .export-section .el-date-picker {
    width: 100% !important;
    margin-right: 0 !important;
  }
}
</style>