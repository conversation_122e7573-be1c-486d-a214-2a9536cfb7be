<template>
  <div class="export-example">
    <div class="example-header">
      <h2>UniversalTableComponent 导出功能示例</h2>
      <div class="export-controls">
        <button @click="basicExport" class="export-btn basic">基础导出</button>
        <button @click="customExport" class="export-btn custom">自定义导出</button>
        <button @click="selectiveExport" class="export-btn selective">选择性导出</button>
      </div>
    </div>

    <UniversalTableComponent
      ref="tableRef"
      :initial-data="sampleData"
      :data-provider="loadSampleData"
      workbook-name="导出示例表格"
      @error="handleError"
      @data-change="handleDataChange"
    />

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import UniversalTableComponent from '../components/UniversalTableComponent.vue'

const tableRef = ref(null)
const errorMessage = ref('')

// 示例数据
const sampleData = reactive({
  '员工信息': [
    ['姓名', '工号', '部门', '职位', '入职日期'],
    ['张三', '001', '技术部', '前端工程师', '2023-01-15'],
    ['李四', '002', '技术部', '后端工程师', '2023-02-20'],
    ['王五', '003', '产品部', '产品经理', '2023-03-10'],
    ['赵六', '004', '设计部', 'UI设计师', '2023-04-05']
  ],
  '部门统计': [
    ['部门', '人数', '平均薪资', '成立时间'],
    ['技术部', 15, 12000, '2020-01-01'],
    ['产品部', 8, 10000, '2020-06-01'],
    ['设计部', 6, 9000, '2021-01-01'],
    ['运营部', 10, 8000, '2021-03-01']
  ],
  '项目进度': [
    ['项目名称', '负责人', '进度', '预计完成时间', '状态'],
    ['用户管理系统', '张三', '85%', '2024-02-15', '进行中'],
    ['数据分析平台', '李四', '60%', '2024-03-20', '进行中'],
    ['移动端应用', '王五', '30%', '2024-04-30', '计划中'],
    ['客服系统升级', '赵六', '100%', '2024-01-31', '已完成']
  ]
})

// 模拟数据提供函数
const loadSampleData = async () => {
  // 模拟异步数据加载
  await new Promise(resolve => setTimeout(resolve, 1000))
  return { ...sampleData }
}

// 基础导出
const basicExport = async () => {
  if (!tableRef.value) return
  await tableRef.value.exportData()
}

// 自定义导出
const customExport = async () => {
  if (!tableRef.value) return
  
  const options = {
    filename: `自定义导出_${new Date().toLocaleDateString()}`,
    headerStyle: {
      font: { bold: true, color: { argb: 'FFFFFFFF' } },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
      alignment: { horizontal: 'center', vertical: 'middle' }
    },
    addBorders: true,
    autoFitColumns: true,
    maxColumnWidth: 30,
    minColumnWidth: 8
  }
  
  const result = await tableRef.value.exportDataWithOptions(options)
  if (result.success) {
    console.log(`成功导出 ${result.sheetsCount} 个工作表到文件: ${result.filename}`)
  }
}

// 选择性导出
const selectiveExport = async () => {
  if (!tableRef.value) return
  
  const options = {
    filename: '员工和部门信息',
    includeSheets: ['员工信息', '部门统计'], // 只导出指定的工作表
    headerStyle: {
      font: { bold: true, size: 12 },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6F3FF' } },
      alignment: { horizontal: 'center', vertical: 'middle' }
    },
    addBorders: true,
    autoFitColumns: true
  }
  
  const result = await tableRef.value.exportDataWithOptions(options)
  if (result.success) {
    console.log(`选择性导出完成: ${result.filename}`)
  }
}

// 错误处理
const handleError = (error) => {
  errorMessage.value = error
  setTimeout(() => {
    errorMessage.value = ''
  }, 5000)
}

// 数据变化处理
const handleDataChange = (data) => {
  console.log('表格数据已更新:', data)
}
</script>

<style scoped>
.export-example {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.example-header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.example-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.export-controls {
  display: flex;
  gap: 12px;
}

.export-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.export-btn.basic {
  background: #4CAF50;
  color: white;
}

.export-btn.basic:hover {
  background: #45a049;
}

.export-btn.custom {
  background: #FF9800;
  color: white;
}

.export-btn.custom:hover {
  background: #F57C00;
}

.export-btn.selective {
  background: #9C27B0;
  color: white;
}

.export-btn.selective:hover {
  background: #7B1FA2;
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #f44336;
  color: white;
  padding: 12px 20px;
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  max-width: 400px;
}
</style>