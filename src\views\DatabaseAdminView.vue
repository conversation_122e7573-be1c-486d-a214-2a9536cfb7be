<!-- d:\infoTech\fipweb\src\views\DatabaseAdminView.vue -->
<template>
  <div class="database-admin-view">
    <h1>数据库后台查改工具</h1>

    <div class="controls">
      <div class="table-selector">
        <label for="tableSelect">选择表名:</label>
        <select id="tableSelect" v-model="selectedTable" :disabled="loading">
          <option value="subject_mapping">科目对照</option>
          <option value="exception_data">异常数据</option>
        </select>
      </div>
      <div class="actions">
        <button @click="executeQuery" :disabled="loading || !selectedTable">
          {{ loading ? '查询中...' : '查询数据' }}
        </button>
        <button @click="executeUpdate" :disabled="loading || !hasData || !selectedTable">
          {{ loading ? '更新中...' : '更新数据' }}
        </button>
        <input
          ref="fileInput"
          type="file"
          accept=".xlsx,.xls"
          @change="handleFileImport"
          style="display: none"
        />
        <button @click="triggerFileImport" :disabled="loading">
          导入Excel
        </button>
        <button @click="clearData" :disabled="loading">
          清空数据
        </button>
      </div>
    </div>

    <div class="table-display-container">
      <VTableComponent
        ref="tableRef"
        :data="tableData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        @data-change="onTableDataChange"
      />
      <p v-if="loading">加载中...</p>
      <p v-if="error" class="error-message">错误: {{ error }}</p>

    </div>
  </div>
</template>

<script>
import VTableComponent from '@/components/VTableComponent.vue';
import * as ExcelJS from 'exceljs';

export default {
  name: 'DatabaseAdminView',
  components: {
    VTableComponent,
  },
  data() {
    return {
      selectedTable: 'subject_mapping', // 默认选择科目对照表
      queryResults: [], // 数据数组，从后端获取或导入
      loading: false,
      error: null,
      // 表格相关属性
      tableWidth: 1200,
      tableHeight: 480, // 减少高度为筛选面板预留空间 (600 - 120 = 480)
    };
  },
  computed: {
    // 将查询结果转换为VTableComponent需要的二维数组格式
    tableData() {
      if (!this.queryResults || this.queryResults.length === 0) {
        return [];
      }

      // 如果 queryResults 已经是二维数组格式，直接返回
      if (Array.isArray(this.queryResults[0])) {
        return this.queryResults;
      }

      // 如果是对象数组，转换为二维数组
      const headers = Object.keys(this.queryResults[0]);
      const dataRows = this.queryResults.map(row =>
        headers.map(header => row[header])
      );

      // 返回二维数组：第一行是表头，后续行是数据
      return [headers, ...dataRows];
    },

    // 检查是否有数据
    hasData() {
      return this.queryResults && this.queryResults.length > 0;
    },
  },
  created() {
    // 组件创建时可以执行初始化逻辑
    console.log('数据库管理视图已创建');
  },
  methods: {
    // 查询数据
    async executeQuery() {
      if (!this.selectedTable) {
        alert('请先选择表名！');
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        const response = await fetch(`/api/db-admin/query?table_name=${this.selectedTable}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        this.queryResults = data; // 直接使用返回的数组数据
        console.log('查询成功，获取到数据:', data.length, '条记录');
      } catch (e) {
        this.error = `查询失败: ${e.message}`;
        console.error('Error executing query:', e);
        // 如果查询失败，显示示例数据
        this.queryResults = this.getExampleData();
      } finally {
        this.loading = false;
      }
    },
    // 更新数据
    async executeUpdate() {
      if (!this.selectedTable) {
        alert('请先选择表名！');
        return;
      }

      if (!this.hasData) {
        alert('没有数据可以更新！');
        return;
      }

      if (!confirm('确定要更新数据吗？此操作将覆盖服务器上的数据！')) {
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        // 获取当前表格数据
        const currentData = this.$refs.tableRef ? this.$refs.tableRef.getData() : this.queryResults;

        const response = await fetch('/api/db-admin/update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            table_name: this.selectedTable,
            data: currentData, // 发送二维数组数据
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        alert(result.message || '数据更新成功！');
        console.log('数据更新成功');
      } catch (e) {
        this.error = `更新失败: ${e.message}`;
        console.error('Error updating data:', e);
        alert(`更新失败: ${e.message}`);
      } finally {
        this.loading = false;
      }
    },
    // 触发文件选择
    triggerFileImport() {
      this.$refs.fileInput.click();
    },

    // 处理文件导入
    async handleFileImport(event) {
      const file = event.target.files[0];
      if (!file) return;

      // 检查文件类型
      if (!file.name.match(/\.(xlsx|xls)$/i)) {
        alert('请选择 Excel 文件 (.xlsx 或 .xls)');
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        const workbook = new ExcelJS.Workbook();
        const arrayBuffer = await file.arrayBuffer();
        await workbook.xlsx.load(arrayBuffer);

        // 获取第一个工作表
        const worksheet = workbook.getWorksheet(1);
        if (!worksheet) {
          throw new Error('Excel 文件中没有找到工作表');
        }

        // 转换为二维数组
        const data = [];
        worksheet.eachRow((row) => {
          const rowData = [];
          row.eachCell({ includeEmpty: true }, (cell) => {
            // 处理不同类型的单元格值
            let value = cell.value;
            if (value && typeof value === 'object') {
              // 处理公式、日期等复杂类型
              if (value.result !== undefined) {
                value = value.result;
              } else if (value instanceof Date) {
                value = value.toISOString().split('T')[0]; // 格式化日期
              } else {
                value = String(value);
              }
            }
            rowData.push(value || '');
          });
          data.push(rowData);
        });

        if (data.length === 0) {
          throw new Error('Excel 文件中没有数据');
        }

        // 更新数据并覆盖表格内容
        this.queryResults = data;

        // 如果有表格引用，直接设置数据
        if (this.$refs.tableRef) {
          this.$refs.tableRef.setData(data);
        }

        console.log(`成功导入 ${data.length} 行数据`);
        alert(`成功导入 ${data.length} 行数据`);

      } catch (error) {
        this.error = `导入失败: ${error.message}`;
        console.error('Excel import error:', error);
        alert(`导入失败: ${error.message}`);
      } finally {
        this.loading = false;
        // 清空文件输入，允许重复选择同一文件
        event.target.value = '';
      }
    },

    // 清空数据
    clearData() {
      if (!confirm('确定要清空所有数据吗？')) {
        return;
      }

      this.queryResults = [];
      this.error = null;

      // 如果有表格引用，也清空表格
      if (this.$refs.tableRef) {
        this.$refs.tableRef.setData([]);
      }

      console.log('数据已清空');
    },

    // 处理表格数据变化事件
    onTableDataChange(newData) {
      // newData 是二维数组格式，直接保存
      this.queryResults = newData;
      console.log('表格数据已更新:', newData.length, '行');
    },


    // 获取示例数据
    getExampleData() {
      if (this.selectedTable === 'subject_mapping') {
        return [
          ['id', 'subject_code', 'subject_name', 'mapping_code', 'mapping_name', 'category', 'status', 'created_date', 'updated_date', 'remark'],
          [1, '1001', '库存现金', 'CASH', '现金', '资产类', '启用', '2024-01-01', '2024-01-01', '现金科目'],
          [2, '1002', '银行存款', 'BANK', '银行', '资产类', '启用', '2024-01-01', '2024-01-01', '银行存款科目'],
          [3, '2001', '应付账款', 'PAYABLE', '应付', '负债类', '启用', '2024-01-01', '2024-01-01', '应付账款科目'],
        ];
      } else if (this.selectedTable === 'exception_data') {
        return [
          ['id', 'data_source', 'error_type', 'error_description', 'original_value', 'suggested_value', 'status', 'created_date', 'processed_date', 'processor', 'remark'],
          [1, '财务系统', '数据格式错误', '金额字段包含非数字字符', 'abc123', '123.00', '待处理', '2024-01-01', null, null, '需要修正金额格式'],
          [2, '合同系统', '数据缺失', '合同编号为空', '', 'CT2024001', '已处理', '2024-01-02', '2024-01-03', '张三', '已补充合同编号'],
          [3, '项目系统', '数据重复', '项目代码重复', 'PRJ001', 'PRJ001_NEW', '待处理', '2024-01-03', null, null, '需要重新分配项目代码'],
        ];
      }
      return [
        ['ID', '名称', '数值', '状态'],
        [1, '示例数据A', 100, 'Active'],
        [2, '示例数据B', 200, 'Inactive'],
        [3, '示例数据C', 150, 'Pending'],
      ];
    },
  },
};
</script>

<style scoped>
.database-admin-view {
  padding: 20px;
  font-family: Arial, sans-serif;
  width: 100%; /* Ensure the main view takes the full width */
}

.controls {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.table-selector {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-selector label {
  font-weight: bold;
  margin-bottom: 0;
  min-width: 80px;
}

.table-selector select {
  min-width: 150px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
}

.actions {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

select, input[type="text"], input[type="number"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}

.param-input {
  margin-bottom: 10px;
}

button {
  padding: 10px 20px;
  margin-right: 10px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

button:hover:enabled {
  background-color: #0056b3;
}

.table-display-container {
  border: 1px solid #ddd;
  min-height: 500px; /* 使用最小高度而不是固定高度 */
  height: auto; /* 自适应内容高度 */
  overflow: visible; /* 修复：改为 visible 让 vtable 内部滚动条正常显示 */
  width: 100%; /* Ensure container takes full width */
  position: relative;

  /* 确保容器不会阻止内部滚动 */
  contain: none;

  /* 为滚动条预留空间 */
  padding: 10px;

  /* 确保表格组件有足够的空间 */
  display: flex;
  flex-direction: column;
}

.loading-message, .error-message {
  text-align: center;
  margin-top: 20px;
  font-weight: bold;
}

.error-message {
  color: red;
}

.debug-info {
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #6c757d;
}

.debug-btn {
  padding: 2px 8px !important;
  margin: 0 0 0 10px !important;
  font-size: 11px !important;
  background-color: #17a2b8 !important;
  border-radius: 3px;
}

.debug-btn:hover {
  background-color: #138496 !important;
}
</style>
