<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        返回主页
      </el-button>
      <h1 class="title">自动制证 - 智能凭证生成</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <el-tooltip v-for="module in modules" :key="module.key" :content="module.tooltip" placement="bottom"
          :show-after="300" effect="light" :popper-style="{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '13px',
            padding: '8px 12px',
            maxWidth: '200px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }">
          <div :class="['nav-tab', { active: activeModule === module.key }]" @click="setActiveModule(module.key)">
            <el-icon>
              <component :is="module.icon" />
            </el-icon>
            <span>{{ module.title }}</span>
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 自动制证面板 -->
      <div v-if="activeModule === 'auto-voucher'" class="auto-voucher-panel">
        <div class="panel-content">
          <h3>自动制证设置</h3>
          <el-form label-width="140px" class="voucher-form">
            <el-form-item label="浏览器选项">
              <el-select v-model="browserOption" placeholder="请选择浏览器选项" style="width: 300px">
                <el-option v-for="option in browserOptions" :key="option.value" :label="option.label"
                  :value="option.value" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <div class="button-group">
                <el-button type="primary" @click="startAutoVoucher" :loading="loading" size="large" class="voucher-btn">
                  {{ loading ? '制证中...' : '自动制证' }}
                </el-button>
                <el-button type="warning" @click="startInternationalAutoVoucher" :loading="internationalLoading" size="large" class="voucher-btn">
                  <el-icon>
                    <Document />
                  </el-icon>
                  {{ internationalLoading ? '制证中...' : '国际单位自动制证' }}
                </el-button>
                <el-button type="success" @click="openChromeTest" :loading="chromeTestLoading" size="large" class="voucher-btn">
                  <el-icon>
                    <ChromeFilled />
                  </el-icon>
                  {{ chromeTestLoading ? '启动中...' : '打开chrome调试模式' }}
                </el-button>
              </div>
            </el-form-item>

            <el-form-item>
              <div class="browser-notice">
                <el-alert title="浏览器使用说明" type="info" :closable="false" show-icon>
                  <template #default>
                    <p>如果使用已打开浏览器，必须使用右边的chrome调试模式进入，并登录司库一体化。自动打开浏览器模式则直接点击</p>
                  </template>
                </el-alert>
              </div>
            </el-form-item>
           </el-form>
        </div>
      </div>

      <!-- 制证日志面板 -->
      <div v-if="activeModule === 'voucher-log'" class="voucher-log-panel">
        <div class="panel-content">
          <h3>制证日志</h3>

          <!-- 查询条件区域 -->
          <div class="query-section">
            <div class="date-inputs">
              <div class="date-item">
                <label class="date-label">开始日期</label>
                <el-date-picker v-model="logStartDate" type="date" placeholder="选择开始日期" format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" class="date-picker" />
              </div>
              <div class="date-item">
                <label class="date-label">结束日期</label>
                <el-date-picker v-model="logEndDate" type="date" placeholder="选择结束日期" format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" class="date-picker" />
              </div>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="queryVoucherLog" :loading="logLoading" size="large" class="action-btn">
                <el-icon>
                  <Search />
                </el-icon>
                {{ logLoading ? '查询中...' : '查询制证日志' }}
              </el-button>
              <el-button type="danger" @click="clearVoucherLog" :loading="clearLoading" size="large" class="action-btn">
                <el-icon>
                  <Delete />
                </el-icon>
                {{ clearLoading ? '清除中...' : '清除制证日志' }}
              </el-button>
            </div>
          </div>


        </div>
      </div>

      <!-- 制证配置面板 -->
      <div v-if="activeModule === 'voucher-config'" class="voucher-config-panel">
        <div class="panel-content">
          <h3>制证配置管理</h3>

          <!-- 排除项目或单位配置 -->
          <el-card class="config-card">
            <template #header>
              <div class="card-header">
                <span>排除项目或单位</span>
                <div>
                  <el-button type="primary" @click="openPasteDialogForExcludedProfitCenters" :icon="DocumentCopy" circle title="粘贴数据"></el-button>
                  <el-button type="success" @click="addExcludedProfitCenterRow" :icon="Plus" circle title="添加行" style="margin-left: 10px"></el-button>
                  <el-button type="danger" @click="batchDelete('excludedProfitCenters')" :icon="Delete" circle title="批量删除" style="margin-left: 10px"></el-button>
                </div>
              </div>
            </template>
            <el-table :data="excludedProfitCentersTableData" style="width: 100%" border @selection-change="onSelectionChange">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="description" label="项目名称或单位名称" min-width="250">
                <template #default="scope">
                  <el-input v-model="scope.row.description" placeholder="请输入项目名称或单位名称"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="isEnabled" label="是否启用" width="100" align="center">
                <template #default="scope">
                  <el-switch v-model="scope.row.isEnabled" active-text="是" inactive-text="否"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope">
                  <el-button type="danger" @click="removeExcludedProfitCenterRow(scope.$index)" :icon="Delete" circle title="删除行"></el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right">
              <el-button type="primary" @click="saveExcludedProfitCenters">保存排除项目或单位</el-button>
            </div>
          </el-card>

          <!-- 单据类型配置 -->
          <el-card class="config-card">
            <template #header>
              <div class="card-header">
                <span>单据类型配置</span>
                <div>
                  <el-button type="primary" @click="openPasteDialogForDocumentTypes" :icon="DocumentCopy" circle title="粘贴数据"></el-button>
                  <el-button type="success" @click="addDocumentTypeRow" :icon="Plus" circle title="添加行" style="margin-left: 10px"></el-button>
                  <el-button type="danger" @click="batchDelete('documentTypes')" :icon="Delete" circle title="批量删除" style="margin-left: 10px"></el-button>
                </div>
              </div>
            </template>
            <el-table :data="documentTypesTableData" style="width: 100%" border @selection-change="onSelectionChange">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="documentTypeDescription" label="单据类型描述" min-width="250">
                <template #default="scope">
                  <el-input v-model="scope.row.documentTypeDescription" placeholder="请输入单据类型描述"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="isEnabled" label="是否启用" width="100" align="center">
                <template #default="scope">
                  <el-switch v-model="scope.row.isEnabled" active-text="是" inactive-text="否"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scope">
                  <el-button type="danger" @click="removeDocumentTypeRow(scope.$index)" :icon="Delete" circle title="删除行"></el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right">
              <el-button type="primary" @click="saveDocumentTypes">保存单据类型</el-button>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 汇率配置面板 -->
      <div v-if="activeModule === 'exchange-rate-config'" class="exchange-rate-config-panel">
        <ExchangeRateConfigComponent />
      </div>
    </div>

    <!-- 消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Delete, Document, ArrowLeft, ChromeFilled, Setting, Plus, DocumentCopy, Money } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'
import ExchangeRateConfigComponent from '@/components/ExchangeRateConfigComponent.vue'

const router = useRouter()
const activeModule = ref('auto-voucher')

const modules = [
  {
    key: 'auto-voucher',
    title: '自动制证',
    icon: 'Document',
    component: 'AutoVoucherPanel',
    tooltip: '智能自动生成财务凭证，支持多种浏览器模式和自动化制证流程'
  },
  {
    key: 'voucher-log',
    title: '制证日志',
    icon: 'List',
    component: 'VoucherLogPanel',
    tooltip: '查看和管理制证历史记录，支持按日期范围查询和清除日志'
  },
  {
    key: 'voucher-config',
    title: '制证配置',
    icon: 'Setting',
    component: 'VoucherConfigPanel',
    tooltip: '配置制证规则，包括排除项目单位和单据类型的管理设置'
  },
  {
    key: 'exchange-rate-config',
    title: '汇率配置',
    icon: 'Money',
    component: 'ExchangeRateConfigPanel',
    tooltip: '配置汇率信息，支持查询和保存汇率数据'
  }
]



const setActiveModule = (key) => {
  activeModule.value = key
}

const goBack = () => {
  router.push('/')
}

// 自动制证面板数据
const browserOption = ref('自动打开浏览器')
const loading = ref(false)
const internationalLoading = ref(false)
const chromeTestLoading = ref(false)
const dialogVisible = ref(false)

const browserOptions = [
  { label: '自动打开浏览器', value: '自动打开浏览器' },
  { label: '使用已打开浏览器', value: '使用已打开浏览器' }
]

// 制证日志面板数据
const logStartDate = ref('')
const logEndDate = ref('')
const logLoading = ref(false)
const clearLoading = ref(false)
const voucherLogs = ref([])

// 制证配置面板数据
const excludedProfitCentersTableData = ref([
  { description: "", isEnabled: true }
])

const documentTypesTableData = ref([
  { documentTypeDescription: "", isEnabled: true }
])

const selectedRows = ref([])

// 查询制证日志
const queryVoucherLog = async () => {
  if (!logStartDate.value || !logEndDate.value) {
    ElMessage.warning('请选择开始日期和结束日期')
    return
  }

  logLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '拉取制证日志',
        '参数': [logStartDate.value, logEndDate.value]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('查询制证日志任务已启动')
    } else {
      ElMessage.error('查询制证日志启动失败')
    }
  } catch (error) {
    ElMessage.error('查询过程中发生错误: ' + error.message)
  } finally {
    logLoading.value = false
  }
}

// 清除制证日志
const clearVoucherLog = async () => {
  if (!logStartDate.value || !logEndDate.value) {
    ElMessage.warning('请选择开始日期和结束日期')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要清除 ${logStartDate.value} 到 ${logEndDate.value} 期间的制证日志吗？`,
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    clearLoading.value = true

    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '清空制证日志',
        '参数': [logStartDate.value, logEndDate.value]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('清除制证日志任务已启动')
    } else {
      ElMessage.error('清除制证日志启动失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清除过程中发生错误: ' + error.message)
    }
  } finally {
    clearLoading.value = false
  }
}

// 打开chrome测试器
const openChromeTest = async () => {
  chromeTestLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '打开chrome浏览器',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('Chrome测试器启动任务已启动')
    } else {
      ElMessage.error('Chrome测试器启动失败')
    }
  } catch (error) {
    ElMessage.error('启动过程中发生错误: ' + error.message)
  } finally {
    chromeTestLoading.value = false
  }
}

// 自动制证功能
const startAutoVoucher = async () => {
  loading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '自动制证',
        '参数': [browserOption.value]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('自动制证任务已启动')
    } else {
      ElMessage.error('自动制证启动失败')
    }
  } catch (error) {
    ElMessage.error('启动过程中发生错误: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 国际单位自动制作功能
const startInternationalAutoVoucher = async () => {
  internationalLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '自动制证',
        '参数': [browserOption.value, true]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('国际单位自动制作任务已启动')
    } else {
      ElMessage.error('国际单位自动制作启动失败')
    }
  } catch (error) {
    ElMessage.error('启动过程中发生错误: ' + error.message)
  } finally {
    internationalLoading.value = false
  }
}

// 配置管理相关函数
const onSelectionChange = (val) => {
  selectedRows.value = val
}

// 统一保存配置函数
const saveConfig = async (configName, data) => {
  try {
    const response = await fetch('http://localhost:8000/api/save-config', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        [configName]: data,
      }),
    })

    if (!response.ok) {
      throw new Error('保存失败')
    }

    return true
  } catch (error) {
    console.error(`保存${configName}失败:`, error)
    throw error
  }
}

// 批量删除功能
const batchDelete = (tableName) => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的行')
    return
  }

  const tableData = {
    excludedProfitCenters: excludedProfitCentersTableData,
    documentTypes: documentTypesTableData,
  }

  ElMessageBox.confirm(`确定删除选中的 ${selectedRows.value.length} 行吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      const data = tableData[tableName].value
      tableData[tableName].value = data.filter(row => !selectedRows.value.includes(row))
      ElMessage.success(`已删除 ${selectedRows.value.length} 行`)
      selectedRows.value = []
    })
    .catch(() => {
      // User cancelled
    })
}

// 排除项目或单位相关函数
const addExcludedProfitCenterRow = () => {
  excludedProfitCentersTableData.value.push({
    description: "",
    isEnabled: true,
  })
}

const removeExcludedProfitCenterRow = (index) => {
  ElMessageBox.confirm("确定删除此排除利润中心吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      excludedProfitCentersTableData.value.splice(index, 1)
      ElMessage.success("排除利润中心已删除")
    })
    .catch(() => {})
}

const saveExcludedProfitCenters = async () => {
  try {
    await saveConfig("excludedProfitCenters", excludedProfitCentersTableData.value)
    ElMessage.success("排除利润中心配置已保存！")
  } catch (error) {
    ElMessage.error("保存排除利润中心配置失败，请稍后重试")
  }
}

const openPasteDialogForExcludedProfitCenters = () => {
  ElMessageBox.prompt(
    "请粘贴数据（每行一条记录，列之间用Tab分隔）：<br><small>预期格式: 利润中心代码 (Tab) 描述 (Tab) 是否启用</small>",
    "粘贴排除利润中心数据",
    {
      confirmButtonText: "确定粘贴",
      cancelButtonText: "取消",
      inputType: "textarea",
      inputPlaceholder: "利润中心代码\t描述\t是否启用\n...",
      dangerouslyUseHTMLString: true,
      customStyle: { width: "600px" },
      inputValidator: (value) => {
        if (!value || value.trim() === "") return "粘贴内容不能为空"
        return true
      },
    }
  )
    .then(({ value }) => {
      processPastedDataForExcludedProfitCenters(value)
    })
    .catch(() => {
      ElMessage.info("粘贴操作已取消")
    })
}

const processPastedDataForExcludedProfitCenters = (pastedText) => {
  const lines = pastedText.trim().split("\n")
  let rowsAdded = 0
  lines.forEach((line) => {
    const columns = line.split("\t")
    if (columns.length >= 1) {
      excludedProfitCentersTableData.value.push({
        description: columns[0]?.trim() || "",
        isEnabled: columns[1]?.trim()?.toLowerCase() === "false" ? false : true,
      })
      rowsAdded++
    }
  })
  if (rowsAdded > 0) {
    ElMessage.success(`${rowsAdded} 行排除利润中心数据已成功粘贴并添加！`)
  } else {
    ElMessage.warning("未粘贴任何数据，或数据格式不正确。")
  }
}

// 单据类型相关函数
const addDocumentTypeRow = () => {
  documentTypesTableData.value.push({
    documentTypeDescription: "",
    isEnabled: true,
  })
}

const removeDocumentTypeRow = (index) => {
  ElMessageBox.confirm("确定删除此单据类型吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      documentTypesTableData.value.splice(index, 1)
      ElMessage.success("单据类型已删除")
    })
    .catch(() => {})
}

const saveDocumentTypes = async () => {
  try {
    await saveConfig("documentTypes", documentTypesTableData.value)
    ElMessage.success("单据类型配置已保存！")
  } catch (error) {
    ElMessage.error("保存单据类型配置失败，请稍后重试")
  }
}

const openPasteDialogForDocumentTypes = () => {
  ElMessageBox.prompt(
    "请粘贴数据（每行一条记录，列之间用Tab分隔）：<br><small>预期格式: 单据类型名称 (Tab) 是否启用</small>",
    "粘贴单据类型数据",
    {
      confirmButtonText: "确定粘贴",
      cancelButtonText: "取消",
      inputType: "textarea",
      inputPlaceholder: "单据类型名称\t是否启用\n...",
      dangerouslyUseHTMLString: true,
      customStyle: { width: "600px" },
      inputValidator: (value) => {
        if (!value || value.trim() === "") return "粘贴内容不能为空"
        return true
      },
    }
  )
    .then(({ value }) => {
      processPastedDataForDocumentTypes(value)
    })
    .catch(() => {
      ElMessage.info("粘贴操作已取消")
    })
}

const processPastedDataForDocumentTypes = (pastedText) => {
  const lines = pastedText.trim().split("\n")
  let rowsAdded = 0
  lines.forEach((line) => {
    const columns = line.split("\t")
    if (columns.length >= 1) {
      documentTypesTableData.value.push({
        documentTypeDescription: columns[0]?.trim() || "",
        isEnabled: columns[1]?.trim()?.toLowerCase() === "false" ? false : true,
      })
      rowsAdded++
    }
  })
  if (rowsAdded > 0) {
    ElMessage.success(`${rowsAdded} 行单据类型数据已成功粘贴并添加！`)
  } else {
    ElMessage.warning("未粘贴任何数据，或数据格式不正确。")
  }
}

// 获取所有配置数据
const fetchAllConfigs = async () => {
  try {
    const response = await fetch("http://localhost:8000/api/get-all-configs", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error("获取配置失败")
    }

    const configs = await response.json()

    // 处理配置数据
    const processConfigData = (data) => {
      if (!data || typeof data !== "object") return data

      if (Array.isArray(data)) {
        return data.map((item) => processConfigData(item))
      }

      const result = { ...data }
      for (const key in result) {
        if (result.hasOwnProperty(key)) {
          if ((key === "isEnabled" || key.endsWith("Enabled")) && result[key] === 1) {
            result[key] = true
          } else if (typeof result[key] === "object") {
            result[key] = processConfigData(result[key])
          }
        }
      }
      return result
    }

    const processedConfigs = processConfigData(configs)

    // 更新配置数据
    if (Array.isArray(processedConfigs.excludedProfitCenters)) {
      excludedProfitCentersTableData.value = processedConfigs.excludedProfitCenters
    }
    if (Array.isArray(processedConfigs.documentTypes)) {
      documentTypesTableData.value = processedConfigs.documentTypes
    }

    ElMessage.success("配置加载成功！")
  } catch (error) {
    console.error("加载配置失败:", error)
    ElMessage.error("加载配置失败，请刷新页面重试")
  }
}

// 组件挂载时加载配置
import { onMounted } from 'vue'
onMounted(() => {
  fetchAllConfigs()
})
</script>

<style scoped>
.statistics-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  position: relative;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
  flex-shrink: 0;
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  overflow: hidden;
  background: #f5f7fa;
  height: 100%;
}

.content-area>* {
  height: 100%;
  overflow: auto;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.nav-tabs::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 面板样式 */
.auto-voucher-panel,
.voucher-log-panel,
.voucher-config-panel,
.exchange-rate-config-panel {
  height: 100%;
  padding: 20px;
  background: white;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: auto;
}

.panel-content h3 {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 30px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e8e8e8;
}

.voucher-form {
  max-width: 600px;
}

.voucher-form .el-form-item {
  margin-bottom: 25px;
}

.voucher-form .el-button {
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-start;
  justify-content: flex-start;
}

.voucher-btn {
  min-width: 180px;
  flex: 0 0 auto;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .voucher-btn {
    width: 100%;
    min-width: unset;
    height: 52px;
  }
}

@media (max-width: 1200px) and (min-width: 769px) {
  .button-group {
    gap: 10px;
  }

  .voucher-btn {
    min-width: 160px;
    font-size: 15px;
    padding: 12px 24px;
    height: 46px;
  }
}

@media (min-width: 1201px) {
  .button-group {
    gap: 16px;
  }

  .voucher-btn {
    min-width: 200px;
    height: 50px;
  }
}

/* 查询区域样式 */
.query-section {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
}

.date-inputs {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
  align-items: flex-end;
}

.date-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 4px;
}

.date-picker {
  width: 220px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: flex-start;
}

.action-btn {
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 配置卡片样式 */
.config-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.config-card .el-table {
  margin-top: 16px;
}

.config-card .el-button {
  margin-left: 8px;
}
</style>
< /style>