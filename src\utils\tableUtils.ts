/**
 * 表格工具函数
 */

import type { TableData, CellValue, CellObject } from '../types/UniversalTable'

/**
 * 将 Excel 数据转换为表格数据格式
 * @param excelData Excel 导入的原始数据
 * @returns 格式化后的表格数据
 */
export function convertExcelToTableData(excelData: any): TableData {
  const result: TableData = {}
  
  if (Array.isArray(excelData)) {
    // 如果是数组，作为单个表格处理
    result['Sheet1'] = excelData
  } else if (typeof excelData === 'object') {
    // 如果是对象，每个属性作为一个表格
    Object.entries(excelData).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        result[key] = value
      }
    })
  }
  
  return result
}

/**
 * 将表格数据转换为 CSV 格式
 * @param tableData 表格数据
 * @param tableName 指定表格名称，不指定则转换所有表格
 * @returns CSV 字符串
 */
export function convertTableDataToCSV(tableData: TableData, tableName?: string): string {
  const tables = tableName ? { [tableName]: tableData[tableName] } : tableData
  let csvContent = ''
  
  Object.entries(tables).forEach(([name, data], index) => {
    if (index > 0) csvContent += '\n\n'
    
    // 添加表格名称作为注释
    if (Object.keys(tables).length > 1) {
      csvContent += `# ${name}\n`
    }
    
    // 转换数据行
    data.forEach(row => {
      const csvRow = row.map(cell => {
        let value = ''
        
        // 处理 Univer 单元格对象格式
        if (cell && typeof cell === 'object' && 'v' in cell) {
          value = String((cell as CellObject).v || '')
        } else {
          value = String(cell || '')
        }
        
        // 如果包含逗号、引号或换行符，需要用引号包围
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          value = `"${value.replace(/"/g, '""')}"`
        }
        
        return value
      })
      
      csvContent += csvRow.join(',') + '\n'
    })
  })
  
  return csvContent
}

/**
 * 下载表格数据为 CSV 文件
 * @param tableData 表格数据
 * @param filename 文件名
 * @param tableName 指定表格名称
 */
export function downloadTableDataAsCSV(
  tableData: TableData, 
  filename: string = 'table-data.csv',
  tableName?: string
): void {
  const csvContent = convertTableDataToCSV(tableData, tableName)
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

/**
 * 验证身份证号格式
 * @param idNumber 身份证号
 * @returns 是否有效
 */
export function validateIdNumber(idNumber: string): boolean {
  if (!idNumber || typeof idNumber !== 'string') return false
  
  // 18位身份证号正则
  const regex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  
  if (!regex.test(idNumber)) return false
  
  // 校验码验证
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
  
  let sum = 0
  for (let i = 0; i < 17; i++) {
    sum += parseInt(idNumber[i]) * weights[i]
  }
  
  const checkCode = checkCodes[sum % 11]
  return checkCode === idNumber[17].toUpperCase()
}

/**
 * 格式化单元格值
 * @param value 原始值
 * @param forceText 是否强制为文本格式
 * @returns 格式化后的值
 */
export function formatCellValue(value: any, forceText: boolean = false): CellValue {
  if (value === null || value === undefined) return ''
  
  if (forceText) {
    return {
      v: String(value),
      t: 1, // CellValueType.STRING
      s: null
    }
  }
  
  return value
}

/**
 * 批量处理表格数据中的身份证号列
 * @param tableData 表格数据
 * @param idColumnNames 身份证号列名数组
 * @returns 处理后的表格数据
 */
export function processIdNumberColumns(
  tableData: TableData, 
  idColumnNames: string[] = ['身份证号', '证件号码', '工号']
): TableData {
  const result: TableData = {}
  
  Object.entries(tableData).forEach(([tableName, data]) => {
    if (!data || data.length === 0) {
      result[tableName] = data
      return
    }
    
    const headerRow = data[0]
    const idColumnIndexes: number[] = []
    
    // 找出身份证号列的索引
    headerRow.forEach((header, index) => {
      if (header && typeof header === 'string') {
        const headerStr = header.trim()
        if (idColumnNames.some(name => headerStr.includes(name))) {
          idColumnIndexes.push(index)
        }
      }
    })
    
    // 处理数据
    const processedData = data.map((row, rowIndex) => {
      if (rowIndex === 0) return row // 保持表头不变
      
      return row.map((cell, colIndex) => {
        if (idColumnIndexes.includes(colIndex) && cell) {
          return formatCellValue(cell, true)
        }
        return cell
      })
    })
    
    result[tableName] = processedData
  })
  
  return result
}

/**
 * 获取表格统计信息
 * @param tableData 表格数据
 * @returns 统计信息
 */
export function getTableStatistics(tableData: TableData): {
  totalTables: number
  totalRows: number
  totalCells: number
  emptyTables: string[]
  largestTable: { name: string; rows: number; cols: number }
} {
  const stats = {
    totalTables: 0,
    totalRows: 0,
    totalCells: 0,
    emptyTables: [] as string[],
    largestTable: { name: '', rows: 0, cols: 0 }
  }
  
  Object.entries(tableData).forEach(([tableName, data]) => {
    stats.totalTables++
    
    if (!data || data.length === 0) {
      stats.emptyTables.push(tableName)
      return
    }
    
    const rows = data.length
    const cols = data[0]?.length || 0
    
    stats.totalRows += rows
    stats.totalCells += rows * cols
    
    if (rows > stats.largestTable.rows) {
      stats.largestTable = { name: tableName, rows, cols }
    }
  })
  
  return stats
}

/**
 * 清理表格数据中的空行和空列
 * @param tableData 表格数据
 * @returns 清理后的表格数据
 */
export function cleanTableData(tableData: TableData): TableData {
  const result: TableData = {}
  
  Object.entries(tableData).forEach(([tableName, data]) => {
    if (!data || data.length === 0) {
      result[tableName] = []
      return
    }
    
    // 过滤空行
    const nonEmptyRows = data.filter(row => 
      row.some(cell => {
        if (cell && typeof cell === 'object' && 'v' in cell) {
          return (cell as CellObject).v !== null && (cell as CellObject).v !== undefined && (cell as CellObject).v !== ''
        }
        return cell !== null && cell !== undefined && cell !== ''
      })
    )
    
    if (nonEmptyRows.length === 0) {
      result[tableName] = []
      return
    }
    
    // 找出最大列数
    const maxCols = Math.max(...nonEmptyRows.map(row => row.length))
    
    // 找出有数据的列
    const hasDataInColumn = new Array(maxCols).fill(false)
    nonEmptyRows.forEach(row => {
      row.forEach((cell, colIndex) => {
        if (cell !== null && cell !== undefined && cell !== '') {
          if (cell && typeof cell === 'object' && 'v' in cell) {
            if ((cell as CellObject).v !== null && (cell as CellObject).v !== undefined && (cell as CellObject).v !== '') {
              hasDataInColumn[colIndex] = true
            }
          } else {
            hasDataInColumn[colIndex] = true
          }
        }
      })
    })
    
    // 移除空列
    const cleanedData = nonEmptyRows.map(row => 
      row.filter((_, colIndex) => hasDataInColumn[colIndex])
    )
    
    result[tableName] = cleanedData
  })
  
  return result
}