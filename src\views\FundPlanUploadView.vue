<template>
  <div class="fund-plan-upload-view">
    <FundPlanUploadComponent @back="handleBack" />
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import FundPlanUploadComponent from '../components/FundPlanUploadComponent.vue'

const router = useRouter()

function handleBack() {
  // 可以根据需要处理返回逻辑
  // 这里可以返回到资金管理主页面或其他页面
  console.log('返回资金管理主页面')
}
</script>

<style scoped>
.fund-plan-upload-view {
  height: calc(100vh - 60px);
  /* 减去可能的导航栏高度 */
  width: 100%;
  overflow: hidden;
}
</style>