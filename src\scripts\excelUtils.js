/**
 * Excel工具类
 * 提供Excel文件的读取、写入、模板生成等功能
 */
import * as ExcelJS from 'exceljs'

/**
 * 创建Excel工作簿的基础样式配置
 */
export const createBaseStyles = () => ({
  headerStyle: {
    font: { bold: true, color: { argb: 'FFFFFF' } },
    fill: {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    },
    alignment: { horizontal: 'center', vertical: 'middle' },
    border: {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    }
  },
  dataStyle: {
    alignment: { horizontal: 'left', vertical: 'middle' },
    border: {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    }
  }
})

/**
 * 读取Excel文件
 * @param {File} file - Excel文件对象
 * @returns {Promise<Array>} 返回二维数组数据
 */
export const readExcelFile = async (file) => {
  try {
    const workbook = new ExcelJS.Workbook()
    const arrayBuffer = await file.arrayBuffer()
    await workbook.xlsx.load(arrayBuffer)
    
    // 获取第一个工作表
    const worksheet = workbook.worksheets[0]
    if (!worksheet) {
      throw new Error('Excel文件中没有找到工作表')
    }
    
    const data = []
    
    // 遍历所有行
    worksheet.eachRow((row, rowNumber) => {
      const rowData = []
      
      // 获取行中的所有单元格值
      row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        let value = cell.value
        
        // 处理不同类型的单元格值
        if (value && typeof value === 'object') {
          if (value.formula) {
            value = value.result || ''
          } else if (value.text) {
            value = value.text
          } else if (value instanceof Date) {
            value = value.toISOString().slice(0, 10)
          } else {
            value = String(value)
          }
        }
        
        rowData.push(value || '')
      })
      
      // 只添加非空行
      if (rowData.some(cell => cell !== '')) {
        data.push(rowData)
      }
    })
    
    return data
  } catch (error) {
    console.error('读取Excel文件失败:', error)
    throw new Error('读取Excel文件失败: ' + error.message)
  }
}

/**
 * 创建Excel工作簿
 * @param {Array} data - 二维数组数据，第一行为表头
 * @param {string} sheetName - 工作表名称
 * @returns {ExcelJS.Workbook} Excel工作簿对象
 */
export const createWorkbook = (data, sheetName = '数据表') => {
  if (!data || data.length === 0) {
    throw new Error('数据不能为空')
  }
  
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet(sheetName)
  const styles = createBaseStyles()
  
  const headers = data[0]
  const dataRows = data.slice(1)

  // 定义身份证相关的表头关键字
  const idCardHeaderKeywords = ['身份证号', '证件号码', '公民身份号码'];
  
  // 设置表头
  const headerRow = worksheet.addRow(headers)
  headerRow.eachCell((cell) => {
    // 按照 exceljs 的推荐方式逐个应用样式属性
    cell.font = styles.headerStyle.font;
    cell.fill = styles.headerStyle.fill;
    cell.alignment = styles.headerStyle.alignment;
    cell.border = styles.headerStyle.border;
  })
  
  // 添加数据行
  dataRows.forEach(row => {
    const dataRow = worksheet.addRow(row)
    dataRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      // 按照 exceljs 的推荐方式逐个应用样式属性
      cell.alignment = styles.dataStyle.alignment;
      cell.border = styles.dataStyle.border;

      const header = headers[colNumber - 1];
      const value = cell.value;

      const isIdColumn = idCardHeaderKeywords.includes(header);
      const isLongNumeric = (typeof value === 'string' && /^\d{15,}$/.test(value)) || 
                            (typeof value === 'number' && String(value).length >= 15);

      // 检查是否为身份证列或长数字字符串，若是则强制设为文本格式
      if (isIdColumn || isLongNumeric) {
        cell.numFmt = '@';
        if (value !== null && value !== undefined) {
          cell.value = String(value); // 直接转换为字符串，不加前缀
        }
      } else if (typeof value === 'string' && !isNaN(value) && value.trim() !== '') {
        // 其他看起来像数字的字符串，转换为数值
        cell.value = parseFloat(value);
        cell.numFmt = '#,##0.00'; // 默认数字格式
      }
    })
  })
  
  // 自动调整列宽
  worksheet.columns.forEach((column, index) => {
    let maxLength = headers[index]?.length || 10
    dataRows.forEach(row => {
      const cellValue = String(row[index] || '')
      maxLength = Math.max(maxLength, cellValue.length)
    })
    column.width = Math.min(Math.max(maxLength + 2, 10), 50)
  })
  
  return workbook
}

/**
 * 导出Excel文件
 * @param {Array} data - 二维数组数据
 * @param {string} filename - 文件名（不含扩展名）
 * @param {string} sheetName - 工作表名称
 */
export const exportExcel = async (data, filename, sheetName = '数据表') => {
  try {
    const workbook = createWorkbook(data, sheetName)
    
    // 生成文件并下载
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}_${new Date().toISOString().slice(0, 10)}.xlsx`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    console.log('导出Excel成功:', filename)
  } catch (error) {
    console.error('导出Excel失败:', error)
    throw new Error('导出Excel失败: ' + error.message)
  }
}

/**
 * 创建多工作表Excel文件
 * @param {Array} sheets - 工作表数组，每个元素包含 {name, data}
 * @param {string} filename - 文件名
 */
export const exportMultiSheetExcel = async (sheets, filename) => {
  try {
    const workbook = new ExcelJS.Workbook()
    const styles = createBaseStyles()
    
    sheets.forEach(({ name, data }) => {
      if (!data || data.length === 0) return
      
      const worksheet = workbook.addWorksheet(name)
      const headers = data[0]
      const dataRows = data.slice(1)
      
      // 设置表头
      const headerRow = worksheet.addRow(headers)
      headerRow.eachCell((cell) => {
        // 按照 exceljs 的推荐方式逐个应用样式属性
        cell.font = styles.headerStyle.font;
        cell.fill = styles.headerStyle.fill;
        cell.alignment = styles.headerStyle.alignment;
        cell.border = styles.headerStyle.border;
      })
      
      // 添加数据行
      dataRows.forEach(row => {
        const dataRow = worksheet.addRow(row)
        dataRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          // 按照 exceljs 的推荐方式逐个应用样式属性
          cell.alignment = styles.dataStyle.alignment;
          cell.border = styles.dataStyle.border;

          const header = headers[colNumber - 1];
          const value = cell.value;
          const idCardHeaderKeywords = ['身份证号', '证件号码', '公民身份号码'];

          const isIdColumn = idCardHeaderKeywords.includes(header);
          const isLongNumeric = (typeof value === 'string' && /^\d{15,}$/.test(value)) || 
                                (typeof value === 'number' && String(value).length >= 15);

          // 检查是否为身份证列或长数字字符串，若是则强制设为文本格式
          if (isIdColumn || isLongNumeric) {
            cell.numFmt = '@';
            if (value !== null && value !== undefined) {
              cell.value = String(value); // 直接转换为字符串，不加前缀
            }
          } else if (typeof value === 'string' && !isNaN(value) && value.trim() !== '') {
            // 其他看起来像数字的字符串，转换为数值
            cell.value = parseFloat(value);
            cell.numFmt = '#,##0.00'; // 默认数字格式
          }
        })
      })
      
      // 自动调整列宽
      worksheet.columns.forEach((column, index) => {
        let maxLength = headers[index]?.length || 10
        dataRows.forEach(row => {
          const cellValue = String(row[index] || '')
          maxLength = Math.max(maxLength, cellValue.length)
        })
        column.width = Math.min(Math.max(maxLength + 2, 10), 50)
      })
    })
    
    // 生成文件并下载
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}_${new Date().toISOString().slice(0, 10)}.xlsx`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    console.log('导出多工作表Excel成功:', filename)
  } catch (error) {
    console.error('导出多工作表Excel失败:', error)
    throw new Error('导出多工作表Excel失败: ' + error.message)
  }
}

/**
 * 创建导入模板
 * @param {Array} headers - 表头数组
 * @param {Array} exampleData - 示例数据行
 * @param {string} filename - 文件名
 * @param {string} sheetName - 工作表名称
 */
export const createImportTemplate = async (headers, exampleData, filename, sheetName = '导入模板') => {
  try {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet(sheetName)
    const styles = createBaseStyles()
    
    // 设置表头
    const headerRow = worksheet.addRow(headers)
    headerRow.eachCell((cell) => {
      // 按照 exceljs 的推荐方式逐个应用样式属性
      cell.font = styles.headerStyle.font;
      cell.fill = styles.headerStyle.fill;
      cell.alignment = styles.headerStyle.alignment;
      cell.border = styles.headerStyle.border;
    })
    
    // 添加示例数据
    if (exampleData && exampleData.length > 0) {
      exampleData.forEach(row => {
        const dataRow = worksheet.addRow(row)
        dataRow.eachCell({ includeEmpty: true }, (cell) => {
          // 按照 exceljs 的推荐方式逐个应用样式属性
          cell.alignment = styles.dataStyle.alignment;
          cell.border = styles.dataStyle.border;
        })
      })
    }
    
    // 自动调整列宽
    worksheet.columns.forEach((column, index) => {
      let maxLength = headers[index]?.length || 10
      if (exampleData && exampleData.length > 0) {
        exampleData.forEach(row => {
          const cellValue = String(row[index] || '')
          maxLength = Math.max(maxLength, cellValue.length)
        })
      }
      column.width = Math.min(Math.max(maxLength + 2, 10), 30)
    })
    
    // 生成文件并下载
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}_${new Date().toISOString().slice(0, 10)}.xlsx`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    console.log('创建导入模板成功:', filename)
  } catch (error) {
    console.error('创建导入模板失败:', error)
    throw new Error('创建导入模板失败: ' + error.message)
  }
}

/**
 * 验证数据格式
 * @param {Array} data - 要验证的数据
 * @param {Array} expectedHeaders - 期望的表头
 * @returns {boolean} 验证结果
 */
export const validateDataFormat = (data, expectedHeaders) => {
  if (!data || data.length === 0) return false
  
  const expectedColumnCount = expectedHeaders.length
  
  // 检查每行的列数
  for (let i = 0; i < data.length; i++) {
    if (data[i].length !== expectedColumnCount) {
      console.warn(`第${i+1}行列数不匹配，期望:${expectedColumnCount}，实际:${data[i].length}`)
      return false
    }
  }
  
  return true
}
