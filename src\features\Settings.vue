<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        返回主页
      </el-button>
      <h1 class="title">设置管理</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <el-tooltip v-for="module in modules" :key="module.key" :content="module.tooltip" placement="bottom"
          :show-after="300" effect="light" :popper-style="{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '13px',
            padding: '8px 12px',
            maxWidth: '200px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }">
          <div :class="['nav-tab', { active: activeModule === module.key }]" @click="setActiveModule(module.key)">
            <el-icon>
              <component :is="module.icon" />
            </el-icon>
            <span>{{ module.title }}</span>
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 配置文件 -->
      <div v-if="activeModule === 'config'" class="config-content">
        <SettingsView />
      </div>

      <!-- 激活注册 -->
      <div v-else-if="activeModule === 'activation'" class="activation-content">
        <div class="activation-form">
          <h2>软件激活注册</h2>
          <el-form label-width="120px">
            <el-form-item label="机器码">
              <el-input v-model="machineCode" readonly placeholder="获取中..." />
            </el-form-item>

            <el-form-item label="注册码">
              <el-input v-model="registrationCode" placeholder="请输入注册码" />
            </el-form-item>

            <el-form-item label="到期时间">
              <el-input v-model="expiryTime" readonly placeholder="未激活" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="registerSoftware" :loading="registering">
                {{ registering ? '注册中...' : '注册' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 更新维护 -->
      <div v-else-if="activeModule === 'maintenance'" class="maintenance-content">
        <div class="maintenance-form">
          <h2>软件更新维护</h2>
          <el-form label-width="120px">
            <el-form-item>
              <el-button type="primary" @click="checkUpdate" :loading="updating">
                {{ updating ? '检查中...' : '检查更新' }}
              </el-button>
            </el-form-item>

            <el-form-item>
              <el-button type="warning" @click="maintenance" :loading="maintaining">
                {{ maintaining ? '维护中...' : '软件维护' }}
              </el-button>
            </el-form-item>

            <el-form-item>
              <el-button type="danger" @click="clearLoginInfo" :loading="clearing">
                {{ clearing ? '清除中...' : '清除登录信息' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 科目对照 -->
      <div v-else-if="activeModule === 'subject-mapping'" class="subject-mapping-content">
        <div class="subject-mapping-header">
          <h2>科目对照管理</h2>
          <div class="subject-mapping-actions">
            <el-button type="primary" @click="importExcelToBackend" :loading="importingExcel" :icon="Upload">
              {{ importingExcel ? '导入中...' : '直接导入Excel到后台' }}
            </el-button>
            <el-button type="success" @click="getRemoteReference" :loading="gettingReference" :icon="Download">
              {{ gettingReference ? '获取中...' : '获取远程参考' }}
            </el-button>
          </div>
        </div>
        <div class="subject-mapping-component">
          <SubjectMappingQueryComponent @back="handleSubjectMappingBack" />
        </div>
      </div>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Upload, Download } from '@element-plus/icons-vue'
import SettingsView from '@/views/SettingsView.vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'
import SubjectMappingQueryComponent from '@/components/SubjectMappingQueryComponent.vue'

const router = useRouter()
const route = useRoute()
const activeModule = ref('config')

// 激活注册相关数据
const machineCode = ref('')
const registrationCode = ref('')
const expiryTime = ref('')
const registering = ref(false)

// 更新维护相关数据
const updating = ref(false)
const maintaining = ref(false)
const clearing = ref(false)
const dialogVisible = ref(false)

// 科目对照相关数据
const importingExcel = ref(false)
const gettingReference = ref(false)

const modules = [
  {
    key: 'config',
    title: '配置文件',
    icon: 'Setting',
    tooltip: '管理系统配置参数，包括数据库连接、接口设置等基础配置'
  },
  {
    key: 'activation',
    title: '激活注册',
    icon: 'Key',
    tooltip: '软件激活和注册管理，查看机器码和注册状态信息'
  },
  {
    key: 'maintenance',
    title: '更新维护',
    icon: 'Tools',
    tooltip: '系统更新检查和维护工具，包括软件升级和数据清理功能'
  },
  {
    key: 'subject-mapping',
    title: '科目对照',
    icon: 'Document',
    tooltip: '管理会计科目对照关系，支持科目映射和数据同步配置'
  }
]

const setActiveModule = (key) => {
  activeModule.value = key
  if (key === 'activation') {
    getMachineInfo()
  }
}

const goBack = () => {
  router.push('/')
}

// 获取机器码和到期时间
const getMachineInfo = async () => {
  try {
    const response = await fetch('http://127.0.0.1:8000/api/machine-info', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      machineCode.value = data.machineCode || ''
      expiryTime.value = data.expiryTime || '未激活'
    } else {
      ElMessage.error('获取机器信息失败')
    }
  } catch (error) {
    ElMessage.error('获取机器信息时发生错误: ' + error.message)
  }
}

// 注册软件
const registerSoftware = async () => {
  if (!registrationCode.value.trim()) {
    ElMessage.warning('请输入注册码')
    return
  }

  registering.value = true
  try {
    const response = await fetch('http://127.0.0.1:8000/api/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        registrationCode: registrationCode.value
      })
    })

    if (response.ok) {
      const data = await response.json()
      expiryTime.value = data.expiryTime || '注册失败'
      ElMessage.success('注册成功')
      registrationCode.value = ''
    } else {
      ElMessage.error('注册失败')
    }
  } catch (error) {
    ElMessage.error('注册过程中发生错误: ' + error.message)
  } finally {
    registering.value = false
  }
}

// 检查更新
const checkUpdate = async () => {
  updating.value = true

  try {
    // 发送POST请求检查更新
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '更新软件',
        '参数': []
      })
    })

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('检查更新任务已启动')
    } else {
      ElMessage.error('检查更新失败')
    }
  } catch (error) {
    ElMessage.error('检查更新过程中发生错误: ' + error.message)
  } finally {
    updating.value = false
  }
}

// 软件维护
const maintenance = async () => {
  maintaining.value = true

  try {
    // 发送POST请求软件维护
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '软件维护',
        '参数': []
      })
    })

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('软件维护任务已启动')
    } else {
      ElMessage.error('软件维护失败')
    }
  } catch (error) {
    ElMessage.error('软件维护过程中发生错误: ' + error.message)
  } finally {
    maintaining.value = false
  }
}

// 清除登录信息
const clearLoginInfo = async () => {
  clearing.value = true

  try {
    // 发送POST请求清除登录信息
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '清除已登录信息',
        '参数': []
      })
    })

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('清除登录信息任务已启动')
    } else {
      ElMessage.error('清除登录信息失败')
    }
  } catch (error) {
    ElMessage.error('清除登录信息过程中发生错误: ' + error.message)
  } finally {
    clearing.value = false
  }
}

// 直接导入Excel到后台
const importExcelToBackend = async () => {
  importingExcel.value = true

  try {
      try {
        const response = await fetch('http://127.0.0.1:8000/api/import-excel-update-db', {
          method: 'POST',
          body: JSON.stringify({
        'tableName': '科目对照',
      })
        })

        if (response.ok) {
          ElMessage.success('Excel导入成功')
        } else {
          ElMessage.error('Excel导入失败')
        }
      } catch (error) {
        ElMessage.error('Excel导入过程中发生错误: ' + error.message)
      } finally {
        importingExcel.value = false
        document.body.removeChild(input)
      }
    }
      catch (error) {
    ElMessage.error('Excel导入过程中发生错误: ' + error.message)
  } finally {
    importingExcel.value = false
  }
}

// 获取远程参考
const getRemoteReference = async () => {
  gettingReference.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '获取科目对照远程参考',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('获取远程参考任务已启动')
    } else {
      ElMessage.error('获取远程参考失败')
    }
  } catch (error) {
    ElMessage.error('获取远程参考过程中发生错误: ' + error.message)
  } finally {
    gettingReference.value = false
  }
}

// 处理科目对照组件返回
const handleSubjectMappingBack = () => {
  // 在设置页面中，不需要处理返回，因为已经在设置页面内部
  console.log('科目对照组件返回事件')
}

// 进入页面时获取机器信息
onMounted(() => {
  // 根据路由参数设置activeModule
  const activeTab = route.query.activeTab
  if (activeTab && modules.some(m => m.key === activeTab)) {
    activeModule.value = activeTab
  }
  
  if (activeModule.value === 'activation') {
    getMachineInfo()
  }
})
</script>

<style scoped>
.statistics-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  width: 100%;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  overflow: hidden;
  background: #f5f7fa;
}

.content-area>* {
  height: 100%;
  overflow: auto;
}

/* 配置文件内容 */
.config-content {
  padding: 20px;
  background: white;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 激活注册内容 */
.activation-content {
  padding: 20px;
  background: white;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.activation-form {
  max-width: 500px;
  margin: 0 auto;
}

.activation-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}

/* 更新维护内容 */
.maintenance-content {
  padding: 20px;
  background: white;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.maintenance-form {
  max-width: 500px;
  margin: 0 auto;
}

.maintenance-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}

/* 维护按钮样式 */
.maintenance-form .el-button {
  width: 220px;
  height: 45px;
  font-size: 15px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.maintenance-form .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.maintenance-form .el-button:active {
  transform: translateY(0);
}

/* 科目对照内容 */
.subject-mapping-content {
  padding: 20px;
  background: white;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: calc(100vh - 200px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.subject-mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8e8e8;
}

.subject-mapping-header h2 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.subject-mapping-actions {
  display: flex;
  gap: 12px;
}

.subject-mapping-actions .el-button {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.subject-mapping-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.subject-mapping-component {
  flex: 1;
  overflow: hidden;
  background: #f8f9fb;
  border-radius: 6px;
  padding: 0;
}

.subject-mapping-component :deep(.subject-mapping-query-component) {
  padding: 0;
  background: transparent;
  min-height: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.subject-mapping-component :deep(.header) {
  display: none;
  /* 隐藏组件内部的标题和返回按钮 */
}

.subject-mapping-component :deep(.query-panel),
.subject-mapping-component :deep(.action-panel),
.subject-mapping-component :deep(.result-panel) {
  margin: 0 0 16px 0;
}

.subject-mapping-component :deep(.result-panel) {
  flex: 1;
  overflow: hidden;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 2px;
}
</style>