<template>
    <div class="book-management">
        <!-- 参数输入和操作按钮行 -->
        <div class="action-buttons">
            <div class="params-section">
                <el-form :model="form" :inline="true" label-width="60px">
                    <el-form-item label="年份">
                        <el-input-number v-model="form.year" :min="2000" :max="2099" placeholder="年份"
                            controls-position="right" style="width: 120px;" />
                    </el-form-item>
                    <el-form-item label="月份">
                        <el-input-number v-model="form.month" :min="1" :max="12" placeholder="月份"
                            controls-position="right" style="width: 120px;" />
                    </el-form-item>
                </el-form>
            </div>
            <div class="button-group">
                <el-button type="primary" @click="getTemplate" :loading="isLoadingTemplate" icon="Download">
                    根据明细账生成模板
                </el-button>
                <el-button type="success" @click="saveTemplate" :loading="isSavingTemplate" icon="Upload">
                    保存
                </el-button>
                <el-button type="success" @click="getTemplate2" :loading="isSavingTemplate" icon="Download">
                    查询
                </el-button>
                <el-button type="warning" @click="executeBatch" :loading="isBatchExecuting" icon="Check">
                    批量执行
                </el-button>
            </div>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
            <UniversalTableComponent ref="tableRef" :initial-data="tableData" :data-provider="dataProvider"
                workbook-name="成册管理" @data-change="handleDataChange" @error="handleError"
                @initialized="handleTableInitialized" />
        </div>

        <!-- 消息轮询对话框 -->
        <MessagePollingDialog v-model="dialogVisible" @dialog-closed="handleDialogClosed" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ChromeFilled } from '@element-plus/icons-vue'
import UniversalTableComponent from '@/components/UniversalTableComponent.vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

// 响应式数据
const tableRef = ref(null)
const tableData = ref({})
const dialogVisible = ref(false)

const form = ref({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1
})

// 加载状态
const isLoadingTemplate = ref(false)
const isSavingTemplate = ref(false)
const isBatchExecuting = ref(false)
const chromeTestLoading = ref(false)

// 打开chrome测试器
const openChromeTest = async () => {
    chromeTestLoading.value = true

    try {
        const response = await fetch('http://127.0.0.1:8000/api/start-function', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                '功能': '打开chrome浏览器',
                '参数': []
            })
        })

        if (response.ok) {
            dialogVisible.value = true
            ElMessage.success('Chrome测试器启动任务已启动')
        } else {
            ElMessage.error('Chrome测试器启动失败')
        }
    } catch (error) {
        ElMessage.error('启动过程中发生错误: ' + error.message)
    } finally {
        chromeTestLoading.value = false
    }
}

// 获取模板
const getTemplate = async () => {
    if (!form.value.year || !form.value.month) {
        ElMessage.warning('请输入年份和月份')
        return
    }

    if (form.value.year < 2000 || form.value.year > 2099) {
        ElMessage.warning('年份必须在2000-2099之间')
        return
    }

    if (form.value.month < 1 || form.value.month > 12) {
        ElMessage.warning('月份必须在1-12之间')
        return
    }

    try {
        isLoadingTemplate.value = true

        const response = await fetch('http://127.0.0.1:8000/api/book-management/template', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                year: form.value.year,
                month: form.value.month
            })
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.code === 200) {
            // 直接赋值，让Vue的响应式系统处理
            tableData.value = result.data
            ElMessage.success('模板数据获取成功')
        } else {
            throw new Error(result.message || '获取模板数据失败')
        }
    } catch (error) {
        console.error('获取模板数据失败:', error)
        ElMessage.error('获取模板数据失败: ' + error.message)
    } finally {
        isLoadingTemplate.value = false
    }
}

// 获取模板
const getTemplate2 = async () => {
    try {
        isLoadingTemplate.value = true

        const response = await fetch('http://127.0.0.1:8000/api/book-management/template2', {
            method: 'GET'
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.code === 200) {
            // 直接赋值，让Vue的响应式系统处理
            tableData.value = result.data
            ElMessage.success('中间数据获取成功')
        } else {
            throw new Error(result.message || '获取中间数据失败')
        }
    } catch (error) {
        console.error('获取中间数据失败:', error)
        ElMessage.error('获取中间数据失败: ' + error.message)
    } finally {
        isLoadingTemplate.value = false
    }
}


// 保存模板
const saveTemplate = async () => {
    try {
        isSavingTemplate.value = true

        // 获取当前表格数据
        const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

        const response = await fetch('http://127.0.0.1:8000/api/book-management/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: currentData,
                timestamp: new Date().toISOString()
            })
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.code === 200) {
            ElMessage.success('模板保存成功')
        } else {
            throw new Error(result.message || '模板保存失败')
        }
    } catch (error) {
        console.error('模板保存失败:', error)
        ElMessage.error('模板保存失败: ' + error.message)
    } finally {
        isSavingTemplate.value = false
    }
}

// 批量执行
const executeBatch = async () => {
    try {
        isBatchExecuting.value = true
        if (!form.value.year || !form.value.month) {
        ElMessage.warning('请输入年份和月份')
        return
    }

    if (form.value.year < 2000 || form.value.year > 2099) {
        ElMessage.warning('年份必须在2000-2099之间')
        return
    }

    if (form.value.month < 1 || form.value.month > 12) {
        ElMessage.warning('月份必须在1-12之间')
        return
    }


        // 获取当前表格数据
        const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

        // 验证数据
        if (!currentData || Object.keys(currentData).length === 0) {
            ElMessage.warning('请先获取模板数据')
            return
        }

        const response = await fetch('http://127.0.0.1:8000/api/start-function', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                '功能': '执行档案成册',
                '参数': [
                form.value.year,
                form.value.month
            ]
            })
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()

        if (result.code === 200) {
            // 显示消息轮询对话框
            dialogVisible.value = true
            ElMessage.success('批量执行任务已启动')
        } else {
            throw new Error(result.message || '批量执行启动失败')
        }
    } catch (error) {
        console.error('批量执行失败:', error)
        ElMessage.error('批量执行失败: ' + error.message)
    } finally {
        isBatchExecuting.value = false
    }
}

// 数据提供函数（用于刷新）
const dataProvider = async () => {
    const response = await fetch('http://127.0.0.1:8000/api/book-management/template', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            year: form.value.year,
            month: form.value.month
        })
    })

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
        return result.data
    } else {
        throw new Error(result.message || '获取数据失败')
    }
}

// 事件处理
const handleDataChange = (data) => {
    console.log('表格数据变化:', data)
}

const handleError = (error) => {
    console.error('表格错误:', error)
    ElMessage.error(error)
}

const handleTableInitialized = () => {
    console.log('表格初始化完成')
}

const handleDialogClosed = () => {
  console.log('对话框已关闭，重新获取模板数据')
  getTemplate2()
}

// 组件挂载时设置默认值为当前年月
onMounted(() => {
    const now = new Date()
    form.value.year = now.getFullYear()
    form.value.month = now.getMonth() + 1
    getTemplate2()
})

</script>

<style scoped>
.book-management {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #f5f7fa;
}

.action-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.params-section {
    display: flex;
    align-items: center;
}

.params-section .el-form {
    margin: 0;
}

.params-section .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;
}

.button-group {
    display: flex;
    gap: 12px;
}

.button-group .el-button {
    min-width: 120px;
}

.table-container {
    flex: 1;
    overflow: hidden;
    background: white;
    margin: 0;
    min-height: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 12px 16px;
    }

    .params-section {
        justify-content: center;
    }

    .params-section .el-form-item {
        margin-right: 12px;
    }

    .button-group {
        flex-wrap: wrap;
        justify-content: center;
    }

    .button-group .el-button {
        flex: 1;
        min-width: auto;
        max-width: 120px;
    }
}
</style>