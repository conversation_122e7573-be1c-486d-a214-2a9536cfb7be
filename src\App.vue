<script setup>
import { RouterView } from "vue-router";
import { onMounted, watch, onUnmounted } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

onMounted(() => {
  // 监听窗口大小变化，调整布局
  window.addEventListener("resize", handleResize);

  // 初始调整
  handleResize();
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 处理窗口大小变化
const handleResize = () => {
  // 确保内容容器高度适应窗口
  const contentWrapper = document.querySelector(".content-wrapper");
  if (contentWrapper) {
    contentWrapper.style.minHeight = `${window.innerHeight}px`;
  }

  // 确保app容器填满视口
  const appContainer = document.querySelector(".app-container");
  if (appContainer) {
    appContainer.style.minHeight = `${window.innerHeight}px`;
  }
};

// 监听路由变化，确保滚动条重置
watch(
  () => router.currentRoute.value,
  () => {
    window.scrollTo(0, 0);

    // 在路由变化后，给DOM一点时间更新，然后再检查布局
    setTimeout(() => {
      handleResize();
    }, 100);
  }
);
</script>

<template>
  <div class="app-container">
    <!-- Main Content Area -->
    <div class="main-content">
      <div class="content-wrapper">
        <RouterView />
      </div>
    </div>
  </div>
</template>

<style>
/* Global CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Global font */
html {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  color: #374151;
  line-height: 1.5;
  width: 100%; /* Ensure html takes full width */
  height: 100%; /* Ensure html takes full height */
}

body {
  width: 100%; /* Ensure body takes full width */
  height: 100%; /* Ensure body takes full height */
  background-color: #f9fafb;
  overflow-x: auto; /* 改为auto，允许水平滚动 */
}

/* App Container */
.app-container {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: visible; /* 改为visible，允许内容溢出 */
}



/* Main Content Styles */
.main-content {
  margin-left: 0;
  width: 100%;
  min-height: 100vh;
  background-color: #f9fafb;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: visible; /* 改为visible，允许内容溢出 */
}

.content-wrapper {
  /* padding: 24px; */
  width: 100%;
  height: auto; /* 改为auto，自动适应内容高度 */
  min-height: calc(100vh - 48px); /* 确保至少有视口高度减去padding */
  flex: 1;
  display: flex;
  position: relative;
  overflow: auto;
}

/* Target the root element of the component rendered by RouterView */
.content-wrapper > :deep(*) {
  width: 100%;
  height: auto; /* 改为auto，自动适应内容高度 */
  max-width: none;
  max-height: none;
  flex: 1;
  overflow: visible;
}



/* 调整body样式，确保滚动正常 */
body {
  width: 100%; /* Ensure body takes full width */
  height: 100%; /* Ensure body takes full height */
  background-color: #f9fafb;
  overflow-x: auto; /* 改为auto，允许水平滚动 */
}


@media (min-width: 1024px) {
  body {
    margin: 0;
    padding: 0;
  }

  #app {
    width: 100%;
    height: auto; /* 改为auto，而不是固定的100vh */
    min-height: 100vh; /* 确保至少填满视口高度 */
  }
}
</style>
