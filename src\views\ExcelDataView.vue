<template>
  <div class="excel-data-view">
    <!-- 操作按钮栏 -->
    <div class="toolbar">
      <button @click="writeEmployeeData" class="btn btn-primary">写入员工数据</button>
      <button @click="writeSalesData" class="btn btn-primary">写入销售数据</button>
      <button @click="writeFinanceData" class="btn btn-primary">写入财务数据</button>
      <button @click="clearAllData" class="btn btn-warning">清空数据</button>
      <button @click="exportData" class="btn btn-success">导出数据</button>
      <button @click="getDataFromSheet" class="btn btn-info">获取数据</button>
      <button @click="debugEditor" class="btn btn-secondary">调试编辑器</button>
      <button @click="testSimpleCell" class="btn btn-secondary">测试单个单元格</button>
    </div>

    <!-- Excel编辑器容器 -->
    <div class="excel-container" v-loading="loading" element-loading-text="Loading...">
      <div id="excel-editor"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { g_sEmpty_bin } from '@/utils/empty_bin'
// @ts-ignore
import {
  initX2TScript,
  initX2T,
  convertBinToDocumentAndDownload,
  c_oAscFileType2,
} from '@/utils/x2t'

const editor = ref<any>(null)
const editorAPI = ref<any>(null)
const loading = ref(false)

// 全局 media 映射对象
const media: { [key: string]: string } = {}

onMounted(async () => {
  loading.value = true
  try {
    await initX2TScript()
    await loadEditorApi()
    await initX2T()
    console.log('Excel editor initialized')

    // 创建Excel编辑器
    await createExcelEditor()
    loading.value = false
  } catch (error) {
    console.error('Failed to initialize Excel editor:', error)
    loading.value = false
  }
})

// 加载编辑器API
function loadEditorApi(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (window.DocsAPI) {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.src = './web-apps/apps/api/documents/api.js'
    script.onload = () => resolve()
    script.onerror = (error) => {
      console.error('Failed to load OnlyOffice API:', error)
      alert('无法加载编辑器组件。请确保已正确安装 OnlyOffice API。')
      reject(error)
    }
    document.head.appendChild(script)
  })
}

// 创建Excel编辑器
async function createExcelEditor() {
  // 使用空的Excel模板
  const emptyBin = g_sEmpty_bin['.xlsx']
  if (!emptyBin) {
    throw new Error('无法获取Excel模板')
  }

  editor.value = new window.DocsAPI.DocEditor('excel-editor', {
    document: {
      title: 'Excel数据表格.xlsx',
      url: 'excel-data-sheet.xlsx',
      fileType: 'xlsx',
      permissions: {
        edit: true,
        chat: false,
        protect: false,
      },
    },
    editorConfig: {
      lang: 'zh',
      customization: {
        help: false,
        about: false,
        hideRightMenu: true,
        features: {
          spellcheck: {
            change: false,
          },
        },
        anonymous: {
          request: false,
          label: 'Guest',
        },
      },
    },
    events: {
      onAppReady: () => {
        // 加载空的Excel文档
        editor.value.sendCommand({
          command: 'asc_openDocument',
          data: { buf: emptyBin },
        })
      },
      onDocumentReady: () => {
        console.log('Excel文档加载完成')
        // 获取 editorAPI 对象
        if (editor.value && typeof editor.value.getEditorAPI === 'function') {
          editor.value.getEditorAPI(function (api: any) {
            editorAPI.value = api
            console.log('已成功获取editorAPI:', api)
          })
        }
      },
      onSave: handleSaveDocument,
      writeFile: handleWriteFile,
    },
  })
}

// 写入员工数据
function writeEmployeeData() {
  if (!editor.value) return

  // 清空现有数据
  clearSheet()

  // 设置表头
  const headers = ['姓名', '年龄', '部门', '职位', '工资']
  headers.forEach((header, index) => {
    const cellAddress = String.fromCharCode(65 + index) + '1'
    setCellValue(cellAddress, header)
    setCellStyle(cellAddress, { bold: true, backgroundColor: '#4472C4', fontColor: '#FFFFFF' })
  })

  // 员工数据
  const employeeData = [
    ['张三', 28, '研发部', '高级工程师', 15000],
    ['李四', 32, '市场部', '市场经理', 12000],
    ['王五', 45, '行政部', '行政主管', 9000],
    ['赵六', 36, '财务部', '财务经理', 13500],
    ['钱七', 29, '人力资源', 'HR专员', 10000],
    ['孙八', 41, '研发部', '技术总监', 25000],
    ['周九', 33, '销售部', '销售经理', 14000],
    ['吴十', 27, '客服部', '客服主管', 8000]
  ]

  // 写入数据
  employeeData.forEach((row, rowIndex) => {
    row.forEach((cellValue, colIndex) => {
      const cellAddress = String.fromCharCode(65 + colIndex) + (rowIndex + 2)
      setCellValue(cellAddress, cellValue.toString())
    })
  })

  // 自动调整列宽
  autoFitColumns('A:E')
}

// 写入销售数据
function writeSalesData() {
  if (!editor.value) return

  clearSheet()

  const headers = ['产品名称', '销售数量', '单价', '总金额', '销售日期']
  headers.forEach((header, index) => {
    const cellAddress = String.fromCharCode(65 + index) + '1'
    setCellValue(cellAddress, header)
    setCellStyle(cellAddress, { bold: true, backgroundColor: '#70AD47', fontColor: '#FFFFFF' })
  })

  const salesData = [
    ['笔记本电脑', 50, 5999, 299950, '2024-01-15'],
    ['台式机', 30, 3999, 119970, '2024-01-16'],
    ['显示器', 80, 1299, 103920, '2024-01-17'],
    ['键盘', 120, 199, 23880, '2024-01-18'],
    ['鼠标', 150, 99, 14850, '2024-01-19'],
    ['音响', 25, 899, 22475, '2024-01-20'],
    ['摄像头', 40, 299, 11960, '2024-01-21'],
    ['耳机', 60, 399, 23940, '2024-01-22']
  ]

  salesData.forEach((row, rowIndex) => {
    row.forEach((cellValue, colIndex) => {
      const cellAddress = String.fromCharCode(65 + colIndex) + (rowIndex + 2)
      setCellValue(cellAddress, cellValue.toString())
    })
  })

  autoFitColumns('A:E')
}

// 写入财务数据
function writeFinanceData() {
  if (!editor.value) return

  clearSheet()

  const headers = ['科目', '收入', '支出', '净额', '备注']
  headers.forEach((header, index) => {
    const cellAddress = String.fromCharCode(65 + index) + '1'
    setCellValue(cellAddress, header)
    setCellStyle(cellAddress, { bold: true, backgroundColor: '#E7E6E6', fontColor: '#000000' })
  })

  const financeData = [
    ['销售收入', 500000, 0, 500000, '主营业务收入'],
    ['办公费用', 0, 15000, -15000, '日常办公支出'],
    ['人员工资', 0, 180000, -180000, '员工薪资'],
    ['租金支出', 0, 24000, -24000, '办公场地租金'],
    ['营销费用', 0, 35000, -35000, '广告宣传费'],
    ['研发投入', 0, 80000, -80000, '技术研发'],
    ['税费', 0, 25000, -25000, '各项税费'],
    ['净利润', 0, 0, 141000, '本期净利润']
  ]

  financeData.forEach((row, rowIndex) => {
    row.forEach((cellValue, colIndex) => {
      const cellAddress = String.fromCharCode(65 + colIndex) + (rowIndex + 2)
      setCellValue(cellAddress, cellValue.toString())

      // 为净额列添加条件格式
      if (colIndex === 3 && rowIndex < financeData.length - 1) {
        const value = parseFloat(cellValue.toString())
        if (value < 0) {
          setCellStyle(cellAddress, { fontColor: '#FF0000' })
        } else if (value > 0) {
          setCellStyle(cellAddress, { fontColor: '#008000' })
        }
      }
    })
  })

  autoFitColumns('A:E')
}

// 清空所有数据
function clearAllData() {
  if (!editor.value) return
  clearSheet()
}

// 清空工作表
function clearSheet() {
  if (!editor.value) return

  // 选择整个工作表并清空
  editor.value.sendCommand({
    command: 'asc_selectAll'
  })

  editor.value.sendCommand({
    command: 'asc_delete'
  })
}

// 设置单元格值
function setCellValue(cellAddress: string, value: string) {
  console.log(`Setting cell ${cellAddress} to value: ${value}`)

  // 方法1: 使用获取到的 editorAPI 对象
  if (editorAPI.value) {
    console.log('Using editorAPI method')
    try {
      // 使用 editorAPI 直接操作
      if (typeof editorAPI.value.insertText === 'function') {
        editorAPI.value.insertText(value)
        console.log(`Successfully set ${cellAddress} = ${value} using editorAPI.insertText`)
        return
      }

      // 尝试其他 editorAPI 方法
      if (typeof editorAPI.value.setCellValue === 'function') {
        editorAPI.value.setCellValue(cellAddress, value)
        console.log(`Successfully set ${cellAddress} = ${value} using editorAPI.setCellValue`)
        return
      }
    } catch (error) {
      console.error('Error using editorAPI:', error)
    }
  }

  // 方法2: 使用 callCommand (标准的 OnlyOffice API 方式)
  if (editor.value && editor.value.callCommand && typeof editor.value.callCommand === 'function') {
    console.log('Using callCommand method')
    try {
      editor.value.callCommand(function () {
        // 使用 OnlyOffice Document Builder API
        if (typeof Api !== 'undefined') {
          const oWorksheet = Api.GetActiveSheet()
          const oRange = oWorksheet.GetRange(cellAddress)
          oRange.SetValue(value)
          console.log(`Successfully set ${cellAddress} = ${value} using callCommand`)
        }
      })
      return
    } catch (error) {
      console.error('Error in callCommand:', error)
    }
  }

  // 方法3: 使用 addEventListener 监听文档就绪后操作
  if (editor.value) {
    console.log('Using addEventListener method')
    try {
      editor.value.addEventListener("documentReady", function () {
        console.log("文档已完全加载，可以操作内容")

        // 获取editorAPI对象
        editor.value.getEditorAPI(function (api: any) {
          console.log("已成功获取editorAPI for cell operation")

          // 尝试使用 API 设置单元格值
          if (api && typeof api.insertText === 'function') {
            api.insertText(value)
            console.log(`Successfully set cell using api.insertText: ${value}`)
          } else if (api && typeof api.setCellValue === 'function') {
            api.setCellValue(cellAddress, value)
            console.log(`Successfully set cell using api.setCellValue: ${cellAddress} = ${value}`)
          }
        })
      })
      return
    } catch (error) {
      console.error('addEventListener method failed:', error)
    }
  }

  // 方法4: 备用的 sendCommand 方法
  if (editor.value) {
    console.log('Using sendCommand fallback methods')

    // 尝试多种不同的命令格式
    const commands = [
      {
        command: 'asc_setCellText',
        data: {
          range: cellAddress,
          text: value
        }
      },
      {
        command: 'setCellValue',
        range: cellAddress,
        value: value
      },
      {
        command: 'insertText',
        text: value,
        range: cellAddress
      }
    ]

    // 依次尝试每种命令格式
    commands.forEach((cmd, index) => {
      try {
        console.log(`Trying sendCommand ${index + 1}:`, cmd)
        editor.value.sendCommand(cmd)
      } catch (error) {
        console.warn(`SendCommand ${index + 1} failed:`, error)
      }
    })
  }
}

// 设置单元格样式
function setCellStyle(cellAddress: string, style: any) {
  if (!editor.value) return

  if (style.bold) {
    editor.value.sendCommand({
      command: 'asc_setCellBold',
      data: {
        range: cellAddress,
        value: true
      }
    })
  }

  if (style.backgroundColor) {
    editor.value.sendCommand({
      command: 'asc_setCellBackgroundColor',
      data: {
        range: cellAddress,
        color: style.backgroundColor
      }
    })
  }

  if (style.fontColor) {
    editor.value.sendCommand({
      command: 'asc_setCellFontColor',
      data: {
        range: cellAddress,
        color: style.fontColor
      }
    })
  }
}

// 自动调整列宽
function autoFitColumns(range: string) {
  if (!editor.value) return

  editor.value.sendCommand({
    command: 'asc_autoFitColumnWidth',
    data: {
      range: range
    }
  })
}

// 导出数据
function exportData() {
  if (!editor.value) return

  // 触发保存
  editor.value.sendCommand({
    command: 'asc_save'
  })
}

// 获取工作表数据
function getDataFromSheet() {
  if (!editor.value) return

  // 获取当前选中区域的数据
  editor.value.sendCommand({
    command: 'asc_getSelectedRange'
  })

  // 获取工作表数据
  editor.value.sendCommand({
    command: 'asc_getWorksheetData',
    callback: (data: any) => {
      console.log('工作表数据:', data)
      alert('数据已输出到控制台，请查看开发者工具')
    }
  })
}

// 调试函数：检查编辑器可用的方法
function debugEditor() {
  if (!editor.value) {
    console.log('Editor not available')
    return
  }

  console.log('=== Editor Object Debug Info ===')
  console.log('Editor object:', editor.value)

  // 列出编辑器的所有可用方法
  const editorMethods = []
  for (const key in editor.value) {
    if (typeof editor.value[key] === 'function') {
      editorMethods.push(key)
    }
  }
  console.log('Editor available methods:', editorMethods)

  // 检查特定方法
  const methodsToCheck = ['callCommand', 'sendCommand', 'getEditorAPI', 'addEventListener']
  methodsToCheck.forEach(method => {
    console.log(`editor.${method}:`, typeof editor.value[method], editor.value[method] ? '✓' : '✗')
  })

  // 检查 editorAPI 对象
  console.log('=== EditorAPI Object Debug Info ===')
  if (editorAPI.value) {
    console.log('EditorAPI object:', editorAPI.value)

    // 列出 editorAPI 的所有可用方法
    const apiMethods = []
    for (const key in editorAPI.value) {
      if (typeof editorAPI.value[key] === 'function') {
        apiMethods.push(key)
      }
    }
    console.log('EditorAPI available methods:', apiMethods)

    // 检查常用的 API 方法
    const apiMethodsToCheck = ['insertText', 'setCellValue', 'selectRange', 'getCellValue', 'setSelection']
    apiMethodsToCheck.forEach(method => {
      console.log(`editorAPI.${method}:`, typeof editorAPI.value[method], editorAPI.value[method] ? '✓' : '✗')
    })
  } else {
    console.log('EditorAPI not available yet. Try getting it manually...')

    // 尝试手动获取 editorAPI
    if (editor.value && typeof editor.value.getEditorAPI === 'function') {
      editor.value.getEditorAPI(function (api: any) {
        console.log('Manually obtained editorAPI:', api)
        if (api) {
          const apiMethods = []
          for (const key in api) {
            if (typeof api[key] === 'function') {
              apiMethods.push(key)
            }
          }
          console.log('Manually obtained editorAPI methods:', apiMethods)
        }
      })
    }
  }

  // 尝试获取文档对象
  if (editor.value.callCommand) {
    console.log('=== Testing callCommand ===')
    try {
      editor.value.callCommand(function () {
        console.log('Inside callCommand')
        console.log('Api object:', typeof Api !== 'undefined' ? Api : 'undefined')
        if (typeof Api !== 'undefined') {
          console.log('Api methods:', Object.getOwnPropertyNames(Api))
          try {
            const worksheet = Api.GetActiveSheet()
            console.log('Active worksheet:', worksheet)
          } catch (e) {
            console.log('Error getting active sheet:', e)
          }
        }
      })
    } catch (e) {
      console.log('callCommand error:', e)
    }
  }
}

// 测试单个单元格设置
function testSimpleCell() {
  if (!editor.value) {
    console.log('Editor not available')
    return
  }

  console.log('=== Testing Simple Cell Setting ===')

  // 测试设置 A1 单元格
  const testCell = 'A1'
  const testValue = 'Hello World'

  console.log(`Testing: Set ${testCell} = "${testValue}"`)
  setCellValue(testCell, testValue)

  // 等待一段时间后检查结果
  setTimeout(() => {
    console.log('Test completed. Check the spreadsheet to see if A1 contains "Hello World"')
    alert('测试完成！请检查表格中的 A1 单元格是否显示 "Hello World"')
  }, 2000)
}

// 保存文档处理
async function handleSaveDocument(event: any) {
  console.log('Save document event:', event)

  if (event.data && event.data.data) {
    const { data, option } = event.data

    await convertBinToDocumentAndDownload(
      data.data,
      'Excel数据表格.xlsx',
      c_oAscFileType2[option.outputformat] || 'XLSX',
    )
  }

  editor.value.sendCommand({
    command: 'asc_onSaveCallback',
    data: { err_code: 0 },
  })
}

// 处理文件写入请求
function handleWriteFile(event: any) {
  try {
    console.log('Write file event:', event)

    const { data: eventData } = event
    if (!eventData) {
      console.warn('No data provided in writeFile event')
      return
    }

    const {
      data: imageData,
      file: fileName,
    } = eventData

    if (!imageData || !(imageData instanceof Uint8Array)) {
      throw new Error('Invalid image data: expected Uint8Array')
    }

    if (!fileName || typeof fileName !== 'string') {
      throw new Error('Invalid file name')
    }

    const fileExtension = fileName.split('.').pop()?.toLowerCase() || 'png'
    const mimeType = getMimeTypeFromExtension(fileExtension)
    const blob = new Blob([imageData], { type: mimeType })
    const objectUrl = URL.createObjectURL(blob)

    media[`media/${fileName}`] = objectUrl
    editor.value.sendCommand({
      command: 'asc_setImageUrls',
      data: {
        urls: media,
      },
    })

    editor.value.sendCommand({
      command: 'asc_writeFileCallback',
      data: {
        path: objectUrl,
        imgName: fileName,
      },
    })
  } catch (error) {
    console.error('Error handling writeFile:', error)
  }
}

function getMimeTypeFromExtension(extension: string): string {
  const mimeMap: { [key: string]: string } = {
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    gif: 'image/gif',
    bmp: 'image/bmp',
    webp: 'image/webp',
    svg: 'image/svg+xml',
  }

  return mimeMap[extension?.toLowerCase()] || 'image/png'
}

// 组件卸载时清理资源
onBeforeUnmount(() => {
  Object.values(media).forEach((url) => {
    if (typeof url === 'string' && url.startsWith('blob:')) {
      URL.revokeObjectURL(url)
    }
  })

  if (editor.value) {
    if (typeof editor.value.destroyEditor === 'function') {
      editor.value.destroyEditor()
    }
  }
})
</script>

<style scoped>
.excel-data-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.toolbar {
  display: flex;
  gap: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  flex-shrink: 0;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #1e7e34;
}

.btn-info {
  background-color: #17a2b8;
  color: white;
}

.btn-info:hover {
  background-color: #138496;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.excel-container {
  flex: 1;
  width: 100%;
}

#excel-editor {
  width: 100%;
  height: 100%;
}
</style>