<template>
  <div class="report-audit-container">
    <!-- 年报稽核模块 -->
    <div class="module-section">
      <div class="module-header">
        <el-icon class="module-icon">
          <DocumentChecked />
        </el-icon>
        <h3>年报稽核</h3>
      </div>

      <div class="path-selection">
        <div class="path-item">
          <label>年报路径：</label>
          <div class="path-input-group">
            <el-input v-model="reportPath" placeholder="请选择年报路径" readonly class="path-input" />
            <el-button @click="selectReportPath" type="primary" :loading="selectingReport" class="select-btn">
              <el-icon>
                <FolderOpened />
              </el-icon>
              选择文件
            </el-button>
          </div>
        </div>

        <div class="path-item">
          <label>参数表路径：</label>
          <div class="path-input-group">
            <el-input v-model="parameterPath" placeholder="请选择参数表路径" readonly class="path-input" />
            <el-button @click="selectParameterPath" type="primary" :loading="selectingParameter" class="select-btn">
              <el-icon>
                <FolderOpened />
              </el-icon>
              选择文件
            </el-button>
          </div>
        </div>

        <div class="path-item">
          <label>稽核类型：</label>
          <el-select v-model="auditType" placeholder="请选择稽核类型" class="audit-type-select">
            <el-option
              v-for="option in auditTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
      </div>

      <div class="action-section">
        <el-button 
          @click="executeReportAudit" 
          type="success" 
          size="large" 
          :loading="executingAudit"
          :disabled="!reportPath || !parameterPath || !auditType" 
          class="execute-btn"
        >
          <el-icon>
            <DocumentChecked />
          </el-icon>
          {{ executingAudit ? '执行中...' : '执行年报稽核' }}
        </el-button>
      </div>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentChecked, FolderOpened } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

// 响应式数据
const reportPath = ref('')
const parameterPath = ref('')
const auditType = ref('')
const selectingReport = ref(false)
const selectingParameter = ref(false)
const executingAudit = ref(false)
const dialogVisible = ref(false)

// 稽核类型选项
const auditTypeOptions = [
  { value: '01-资产类', label: '01-资产类' },
  { value: '02-负债类', label: '02-负债类' },
  { value: '03-损益类', label: '03-损益类' },
  { value: '04-税务类', label: '04-税务类' },
  { value: '05-主表&权益类', label: '05-主表&权益类' },
  { value: '06-关联方&分部报告', label: '06-关联方&分部报告' }
]

// 选择年报路径
const selectReportPath = async () => {
  selectingReport.value = true
  try {
    const response = await fetch('http://localhost:8000/api/report-assistant/select-current-year-path', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        reportPath.value = data.path
        ElMessage.success('年报路径选择成功')
      } else {
        ElMessage.error(data.message || '选择路径失败')
      }
    } else {
      ElMessage.error('选择路径请求失败')
    }
  } catch (error) {
    console.error('选择年报路径失败:', error)
    ElMessage.error('选择路径失败')
  } finally {
    selectingReport.value = false
  }
}

// 选择参数表路径
const selectParameterPath = async () => {
  selectingParameter.value = true
  try {
    const response = await fetch('http://localhost:8000/api/get-file-path', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        "title": "选择参数表"
      })

    })

    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        parameterPath.value = data.path
        ElMessage.success('参数表路径选择成功')
      } else {
        ElMessage.error(data.message || '选择路径失败')
      }
    } else {
      ElMessage.error('选择路径请求失败')
    }
  } catch (error) {
    console.error('选择参数表路径失败:', error)
    ElMessage.error('选择路径失败')
  } finally {
    selectingParameter.value = false
  }
}

// 执行年报稽核
const executeReportAudit = async () => {
  if (!reportPath.value || !parameterPath.value || !auditType.value) {
    ElMessage.warning('请先选择年报路径、参数表路径和稽核类型')
    return
  }

  executingAudit.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '批量审核',
        '参数': [reportPath.value, parameterPath.value, auditType.value]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('年报稽核任务已启动')
    } else {
      ElMessage.error('年报稽核启动失败')
    }
  } catch (error) {
    ElMessage.error('执行过程中发生错误: ' + error.message)
  } finally {
    executingAudit.value = false
  }
}

// 获取初始路径
const fetchInitialPaths = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/report-assistant/select-current-year-path', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      reportPath.value = data.reportPath || ''
      parameterPath.value = data.parameterPath || ''
      auditType.value = data.auditType || ''
    }
  } catch (error) {
    console.error('获取初始路径失败:', error)
    ElMessage.warning('获取初始路径失败，请手动选择')
  }
}

// 组件挂载时获取初始路径
onMounted(() => {
  //fetchInitialPaths()
})
</script>

<style scoped>
.report-audit-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f5f7fa;
  min-height: 100%;
}

.module-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.module-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f2f5;
}

.module-icon {
  font-size: 24px;
  color: #1976d2;
}

.module-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.path-selection {
  margin-bottom: 24px;
}

.path-item {
  margin-bottom: 16px;
}

.path-item label {
  display: block;
  margin-bottom: 8px;
  color: #5a6c7d;
  font-weight: 500;
  font-size: 14px;
}

.path-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.path-input {
  flex: 1;
}

.audit-type-select {
  width: 100%;
}

.select-btn {
  min-width: 120px;
  height: 40px;
}

.action-section {
  display: flex;
  justify-content: center;
  padding-top: 16px;
}

.execute-btn {
  min-width: 160px;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-audit-container {
    padding: 16px;
  }

  .module-section {
    padding: 16px;
  }

  .path-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .select-btn {
    min-width: auto;
  }
}

/* 按钮悬停效果 */
.execute-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.select-btn:hover {
  transform: translateY(-1px);
}

/* 加载状态样式 */
.execute-btn:loading,
.select-btn:loading {
  opacity: 0.7;
}
</style>