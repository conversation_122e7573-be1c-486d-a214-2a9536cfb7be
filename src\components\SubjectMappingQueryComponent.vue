<template>
  <div class="subject-mapping">
    <!-- 操作按钮行 -->
    <div class="action-buttons">
      <el-button type="primary" @click="getTemplateData" :loading="isLoadingTemplate" icon="Download">
        查询
      </el-button>
      <el-button type="success" @click="updateData" :loading="isUpdatingData" icon="Refresh">
        保存
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <UniversalTableComponent ref="tableRef" :initial-data="tableData" :data-provider="dataProvider"
        workbook-name="科目对照" @data-change="handleDataChange" @error="handleError"
        @initialized="handleTableInitialized" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import UniversalTableComponent from './UniversalTableComponent.vue'

// 定义事件
const emit = defineEmits(['back'])

// 响应式数据
const tableRef = ref(null)
const tableData = ref({})

// 加载状态
const isLoadingTemplate = ref(false)
const isUpdatingData = ref(false)

// 模拟数据转换为对象格式
const mockData = {
  '科目对照': [
    ['总账科目长文本', '科目分类1', '科目分类2', '科目方向' ],
    ['银行存款-工商银行', '资产类', '流动资产', '借方'],
    ['银行存款-建设银行', '资产类', '流动资产', '借方'],
    ['应收账款-客户A', '资产类', '流动资产', '借方'],
    ['应收账款-客户B', '资产类', '流动资产', '借方'],
  ]
}

// 获取模板数据
const getTemplateData = async () => {
  try {
    isLoadingTemplate.value = true

    const response = await fetch('http://127.0.0.1:8000/api/subject-mapping/template', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      // 直接赋值，让Vue的响应式系统处理
      tableData.value = result.data

      ElMessage.success('科目对照数据获取成功')
    } else {
      throw new Error(result.message || '获取科目对照数据失败')
    }
  } catch (error) {
    console.error('获取科目对照数据失败:', error)
    ElMessage.warning('API调用失败，使用模拟数据')

    // 使用模拟数据
    tableData.value = mockData
    ElMessage.success('科目对照数据获取成功（模拟数据）')
  } finally {
    isLoadingTemplate.value = false
  }
}

// 更新数据
const updateData = async () => {
  try {
    isUpdatingData.value = true

    // 获取当前表格数据
    const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

    const response = await fetch('http://127.0.0.1:8000/api/subject-mapping/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        currentData: currentData,
        timestamp: new Date().toISOString()
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      // 更新表格数据
      if (result.data) {
        tableData.value = result.data
      }

      ElMessage.success('科目对照数据更新成功')
    } else {
      throw new Error(result.message || '科目对照数据更新失败')
    }
  } catch (error) {
    console.error('科目对照数据更新失败:', error)
    ElMessage.error('科目对照数据更新失败: ' + error.message)
  } finally {
    isUpdatingData.value = false
  }
}

// 数据提供函数（用于刷新）
const dataProvider = async () => {
  const response = await fetch('http://127.0.0.1:8000/api/subject-mapping/template', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const result = await response.json()

  if (result.code === 200) {
    return result.data
  } else {
    throw new Error(result.message || '获取数据失败')
  }
}

// 事件处理
const handleDataChange = (data) => {
  console.log('表格数据变化:', data)
}

const handleError = (error) => {
  console.error('表格错误:', error)
  ElMessage.error(error)
}

const handleTableInitialized = () => {
  console.log('表格初始化完成')
}

// 组件挂载后自动执行getTemplateData
onMounted(() => {
  getTemplateData()
})
</script>

<style scoped>
.subject-mapping {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f7fa;
}

.action-buttons {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.action-buttons .el-button {
  min-width: 120px;
}

.table-container {
  flex: 1;
  overflow: hidden;
  background: white;
  margin: 0;
}
</style>
