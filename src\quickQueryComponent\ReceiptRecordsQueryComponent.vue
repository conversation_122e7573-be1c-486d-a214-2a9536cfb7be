<template>
  <BaseQueryComponent
    title="收款台账"
    :query-fields="queryFields"
    :mock-data="mockData"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

defineEmits(['back'])

const queryFields = [
  {
    key: 'receiptNumber',
    label: '收款单号',
    type: 'text',
    placeholder: '请输入收款单号',
    width: '180px'
  },
  {
    key: 'payerName',
    label: '付款方',
    type: 'text',
    placeholder: '请输入付款方名称',
    width: '200px'
  },
  {
    key: 'receiptDate',
    label: '收款日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'amount',
    label: '收款金额',
    type: 'amount-range'
  },
  {
    key: 'receiptMethod',
    label: '收款方式',
    type: 'select',
    placeholder: '请选择收款方式',
    width: '150px',
    options: [
      { label: '银行转账', value: 'transfer' },
      { label: '现金收款', value: 'cash' },
      { label: '支票', value: 'check' },
      { label: '承兑汇票', value: 'draft' }
    ]
  }
]

const mockData = [
  ['收款单号', '付款方', '收款金额', '收款日期', '收款方式', '收款用途', '关联合同', '经办人', '到账状态', '备注'],
  ['SK202401001', '甲方建设公司', '800000.00', '2024-01-10', '银行转账', '工程进度款', 'HT202401001', '张三', '已到账', '第一期'],
  ['SK202401002', '乙方开发商', '500000.00', '2024-01-15', '银行转账', '材料款', 'HT202401002', '李四', '已到账', ''],
  ['SK202401003', '丙方投资公司', '1200000.00', '2024-01-20', '承兑汇票', '项目启动资金', 'HT202401003', '王五', '待到账', '6个月期'],
  ['SK202401004', '丁方物业公司', '150000.00', '2024-01-25', '现金收款', '服务费', 'HT202401004', '赵六', '已到账', '季度服务费'],
  ['SK202401005', '戊方租赁公司', '80000.00', '2024-01-30', '银行转账', '租金收入', 'HT202401005', '孙七', '已到账', '月租金']
]
</script>
