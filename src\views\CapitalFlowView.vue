<template>
  <div class="capital-flow-container">
    <h1>资金流动分析</h1>

    <!-- 时间段选择 -->
    <div class="time-selector-section">
      <div class="date-range-container">
        <el-date-picker
          v-model="startDate"
          type="date"
          placeholder="开始日期"
          size="large"
          style="width: 150px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :clearable="true"
          @change="handleStartDateChange"
        />
        <span class="date-separator">至</span>
        <el-date-picker
          v-model="endDate"
          type="date"
          placeholder="结束日期"
          size="large"
          style="width: 150px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :clearable="true"
          @change="handleEndDateChange"
        />
      </div>

      <div class="quick-select-buttons">
        <el-button size="small" @click="setDateRange('week')">最近一周</el-button>
        <el-button size="small" @click="setDateRange('month')">最近一个月</el-button>
        <el-button size="small" @click="setDateRange('quarter')">最近三个月</el-button>
      </div>

      <div class="action-buttons">
        <el-button type="primary" @click="searchData" size="large">
          <el-icon><Search /></el-icon> 查询
        </el-button>
        <el-button @click="resetFilters" size="large">
          <el-icon><Refresh /></el-icon> 重置
        </el-button>
      </div>
    </div>

    <!-- 资金情况卡片 -->
    <div class="fund-status-section">
      <div class="section-header">
        <h2>资金情况</h2>
      </div>
      <div class="fund-status-table" v-loading="loading">
        <SimTableComponent
          v-if="fundStatusData.length > 0"
          :data="fundStatusData"
          :width="1000"
          :height="200"
          :show-filter="false"
        />
        <div v-else class="no-data">请选择日期范围并点击查询按钮获取数据</div>
      </div>
    </div>

    <!-- 收款情况 -->
    <div class="receipt-section">
      <div class="section-header">
        <h2>收款情况</h2>
      </div>
      <div class="receipt-content" v-loading="loading">
        <div class="receipt-table">
          <SimTableComponent
            v-if="receiptData.length > 0"
            :data="receiptData"
            :width="600"
            :height="300"
            :show-filter="false"
          />
          <div v-else class="no-data">暂无收款数据</div>
        </div>
        <div class="receipt-chart">
          <div id="receiptPieChart" class="chart-small"></div>
        </div>
      </div>
    </div>

    <!-- 付款情况 - 二维表格 -->
    <div class="payment-section">
      <div class="section-header">
        <h2>付款情况</h2>
      </div>
      <div class="payment-content" v-loading="loading">
        <div class="payment-table">
          <SimTableComponent
            v-if="paymentPivotData.length > 0"
            :data="paymentPivotData"
            :width="900"
            :height="350"
            :show-filter="false"
          />
          <div v-else class="no-data">暂无付款数据</div>
        </div>
        <div class="payment-chart">
          <div id="paymentPieChart" class="chart-small"></div>
        </div>
      </div>
    </div>

    <!-- 其他资金付款情况 -->
    <div class="payment-section">
      <div class="section-header">
        <h2>其他资金付款情况</h2>
      </div>
      <div class="payment-content" v-loading="loading">
        <div class="payment-table">
          <SimTableComponent
            v-if="otherPaymentData.length > 0"
            :data="otherPaymentData"
            :width="900"
            :height="350"
            :show-filter="false"
          />
          <div v-else class="no-data">暂无其他资金付款数据</div>
        </div>
        <div class="payment-chart">
          <div id="otherPaymentPieChart" class="chart-small"></div>
        </div>
      </div>
    </div>

    <!-- 资金分布饼图 -->
    <div class="chart-section">
      <div class="section-header">
        <h2>资金流动分析</h2>
        <div class="chart-tabs">
          <el-radio-group v-model="chartType" @change="switchChart" size="small">
            <el-radio-button label="pie">饼图分析</el-radio-button>
            <el-radio-button label="waterfall">瀑布图</el-radio-button>
            <el-radio-button label="sankey">桑基图</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="chart-container">
        <div id="fundDistributionChart" class="chart" v-show="chartType === 'pie'"></div>
        <div id="waterfallChart" class="chart" v-show="chartType === 'waterfall'"></div>
        <div id="sankeyChart" class="chart" v-show="chartType === 'sankey'"></div>
      </div>
    </div>

    <!-- 资金明细 -->
    <div class="detail-section">
      <div class="section-header">
        <h2>资金明细</h2>
      </div>
      <el-tabs v-model="activeDetailTab" type="border-card" class="detail-tabs">
        <el-tab-pane label="收款明细" name="receipt">
          <NativeTableComponent
            v-if="receiptDetailData.length > 0"
            :columns="receiptDetailColumns"
            :data="receiptDetailData"
            :height="400"
            :width="1600"
          />
          <div v-else class="no-data">暂无收款明细数据</div>
        </el-tab-pane>
        <el-tab-pane label="付款明细" name="payment">
          <NativeTableComponent
            v-if="paymentDetailData.length > 0"
            :columns="paymentDetailColumns"
            :data="paymentDetailData"
            :height="400"
            :width="1600"
          />
          <div v-else class="no-data">暂无付款明细数据</div>
        </el-tab-pane>
        <el-tab-pane label="其他资金付款明细" name="otherPayment">
          <NativeTableComponent
            v-if="otherPaymentDetailData.length > 0"
            :columns="otherPaymentDetailColumns"
            :data="otherPaymentDetailData"
            :height="400"
            :width="1600"
          />
          <div v-else class="no-data">暂无其他资金明细数据</div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import SimTableComponent from "@/components/SimTableComponent.vue";
import NativeTableComponent from "@/components/NativeTableComponent.vue";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import { Search, Refresh } from '@element-plus/icons-vue'

// 页面状态
const startDate = ref(new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0]);
const endDate = ref(new Date().toISOString().split('T')[0]);

const chartType = ref('pie'); // 图表类型：pie, waterfall, sankey
const activeDetailTab = ref('receipt'); // 明细表激活的tab

// 计算属性：兼容原有的dateRange逻辑
const dateRange = computed(() => {
  if (startDate.value && endDate.value) {
    return [new Date(startDate.value), new Date(endDate.value)];
  }
  return null;
});



// 数据状态
const fundStatusData = ref([]);
const receiptData = ref([]);
const paymentPivotData = ref([]);
const otherPaymentData = ref([]);
const receiptDetailData = ref([]);
const paymentDetailData = ref([]);
const otherPaymentDetailData = ref([]);

const fundDistributionData = ref([]);
const loading = ref(false);

const detailParams = computed(() => ({
  startDate: startDate.value,
  endDate: endDate.value,
}));


// API请求函数
async function fetchCapitalFlowData() {
  if (!startDate.value || !endDate.value) {
    ElMessage.warning('请选择开始和结束日期');
    return;
  }

  loading.value = true;
  try {
    const start = startDate.value;
    const end = endDate.value;

    const response = await fetch('http://127.0.0.1:8000/api/capital-flow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        start_date: start,
        end_date: end
      })
    });

    if (!response.ok) {
      throw new Error('网络请求失败');
    }

    const data = await response.json();

    // 更新各个数据
    fundStatusData.value = data.fund_status_data;
    receiptData.value = data.receipt_data;
    paymentPivotData.value = data.payment_pivot_data;
    otherPaymentData.value = data.other_payment_data; // 新增
    fundDistributionData.value = data.fund_distribution_data;

    // 更新明细数据
    receiptDetailData.value = data.receipt_details || [];
    paymentDetailData.value = data.payment_details || [];
    otherPaymentDetailData.value = data.other_payment_details || [];

    // 重新初始化图表
    // 使用 requestAnimationFrame 确保在下一帧渲染时执行，避免跳动
    requestAnimationFrame(() => {
      // 先初始化图表
      switchChart();
      initReceiptPieChart();
      initPaymentPieChart();
      initOtherPaymentPieChart(); // 新增
      
      // 然后在另一个动画帧中重置滚动位置，确保在所有渲染完成后执行
      requestAnimationFrame(() => {
        const container = document.querySelector('.capital-flow-container');
        if (container) {
          container.scrollTop = 0;
        }
      });
    });

    ElMessage.success('数据加载成功');
  } catch (error) {
    console.error('获取资金流动数据失败:', error);
    ElMessage.error('数据加载失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}





// 格式化数字
function formatNumber(num) {
  return new Intl.NumberFormat("zh-CN").format(num);
}

// 解析金额字符串，支持"¥1,043.60万"这种格式
function parseAmountString(amountStr) {
  if (!amountStr || typeof amountStr !== 'string') {
    return 0;
  }

  // 移除¥符号和逗号
  let cleanStr = amountStr.replace(/[¥,]/g, '');

  // 检查是否包含"万"
  if (cleanStr.includes('万')) {
    const numStr = cleanStr.replace('万', '');
    const num = parseFloat(numStr);
    return isNaN(num) ? 0 : num * 10000;
  }

  // 检查是否包含"亿"
  if (cleanStr.includes('亿')) {
    const numStr = cleanStr.replace('亿', '');
    const num = parseFloat(numStr);
    return isNaN(num) ? 0 : num * 100000000;
  }

  // 普通数字
  const num = parseFloat(cleanStr);
  return isNaN(num) ? 0 : num;
}

// 处理开始日期变化
function handleStartDateChange(value) {
  startDate.value = value;
  // 如果开始日期晚于结束日期，自动调整结束日期
  if (value && endDate.value && new Date(value) > new Date(endDate.value)) {
    endDate.value = value;
  }
}

// 处理结束日期变化
function handleEndDateChange(value) {
  endDate.value = value;
  // 如果结束日期早于开始日期，自动调整开始日期
  if (value && startDate.value && new Date(value) < new Date(startDate.value)) {
    startDate.value = value;
  }
}

// 设置日期范围快捷选项
function setDateRange(type) {
  const now = new Date();
  const end = now.toISOString().split('T')[0];
  let start;

  switch (type) {
    case 'week':
      start = new Date(now.setDate(now.getDate() - 7)).toISOString().split('T')[0];
      break;
    case 'month':
      start = new Date(now.setMonth(now.getMonth() - 1)).toISOString().split('T')[0];
      break;
    case 'quarter':
      start = new Date(now.setMonth(now.getMonth() - 3)).toISOString().split('T')[0];
      break;
    default:
      return;
  }

  startDate.value = start;
  endDate.value = end;
}

// 处理日期变化（保持兼容性）
function handleDateChange() {
  // 日期变化时不自动加载，等用户点击查询按钮
}

// 搜索数据
function searchData() {
  fetchCapitalFlowData();
}

// 重置筛选条件
function resetFilters() {
  const now = new Date();
  startDate.value = new Date(now.setMonth(now.getMonth() - 1)).toISOString().split('T')[0];
  endDate.value = new Date().toISOString().split('T')[0];

  // 清空数据
  fundStatusData.value = [];
  receiptData.value = [];
  paymentPivotData.value = [];
  pendingPaymentData.value = [];
  fundDistributionData.value = [];
  ElMessage.success("已重置筛选条件");
}



// 初始化资金流动图表 - 高级可视化
function initFundDistributionChart() {
  const chartDom = document.getElementById("fundDistributionChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 处理资金流动数据，区分流入和流出
  let processedData = [];
  let inflowTotal = 0;
  let outflowTotal = 0;

  if (fundDistributionData.value.length > 0) {
    fundDistributionData.value.forEach(item => {
      if (item.value > 0) {
        processedData.push({
          ...item,
          name: `${item.name} (流入)`,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#67C23A' },
              { offset: 0.5, color: '#85CE61' },
              { offset: 1, color: '#95D475' }
            ])
          }
        });
        inflowTotal += item.value;
      } else if (item.value < 0) {
        processedData.push({
          ...item,
          value: Math.abs(item.value),
          name: `${item.name} (流出)`,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#F56C6C' },
              { offset: 0.5, color: '#F78989' },
              { offset: 1, color: '#F9A7A7' }
            ])
          }
        });
        outflowTotal += Math.abs(item.value);
      }
    });
  } else {
    processedData = [{ value: 0, name: '暂无数据' }];
  }

  const option = {
    title: [
      {
        text: '资金流动分析',
        left: 'center',
        top: '5%',
        textStyle: {
          fontSize: 20,
          fontWeight: 'bold',
          color: '#2c3e50'
        }
      },
      {
        text: `净流入: ¥${formatNumber(inflowTotal - outflowTotal)}`,
        left: '15%',
        top: '15%',
        textStyle: {
          fontSize: 14,
          color: inflowTotal > outflowTotal ? '#67C23A' : '#F56C6C',
          fontWeight: 'bold'
        }
      },
      {
        text: `流入总额: ¥${formatNumber(inflowTotal)}`,
        right: '15%',
        top: '15%',
        textStyle: {
          fontSize: 12,
          color: '#67C23A'
        }
      },
      {
        text: `流出总额: ¥${formatNumber(outflowTotal)}`,
        right: '15%',
        top: '20%',
        textStyle: {
          fontSize: 12,
          color: '#F56C6C'
        }
      }
    ],
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      formatter: function(params) {
        const isInflow = params.name.includes('流入');
        const arrow = isInflow ? '↗️' : '↘️';
        const type = isInflow ? '资金流入' : '资金流出';
        return `${arrow} ${type}<br/>${params.name}<br/>金额: ¥${formatNumber(params.value)}<br/>占比: ${params.percent}%`;
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: '5%',
      top: '30%',
      bottom: '10%',
      data: processedData.map(item => item.name),
      textStyle: {
        color: '#333',
        fontSize: 12
      },
      pageIconColor: '#409EFF',
      pageIconInactiveColor: '#C0C4CC',
      pageTextStyle: {
        color: '#666'
      }
    },
    series: [
      {
        name: '资金流动',
        type: 'pie',
        radius: ['35%', '65%'],
        center: ['40%', '60%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 3,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.2)'
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 11,
          fontWeight: 'bold',
          formatter: function(params) {
            const isInflow = params.name.includes('流入');
            const arrow = isInflow ? '↗️' : '↘️';
            return `${arrow} {b|${params.name.replace(/ \(流[入出]\)/, '')}}\n{c|¥${formatNumber(params.value)}}`;
          },
          rich: {
            b: {
              fontSize: 11,
              fontWeight: 'bold',
              color: '#333'
            },
            c: {
              fontSize: 10,
              color: '#666'
            }
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 0, 0, 0.4)'
          },
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          },
          scaleSize: 5
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          smooth: true,
          lineStyle: {
            width: 2
          }
        },
        data: processedData,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200;
        }
      }
    ],
    graphic: [
      {
        type: 'text',
        left: 'center',
        bottom: '5%',
        style: {
          text: '💡 提示：绿色表示资金流入，红色表示资金流出',
          fontSize: 12,
          fill: '#999'
        }
      }
    ]
  };

  myChart.setOption(option);

  // 添加点击事件
  myChart.on('click', function(params) {
    if (params.data && params.data.value > 0) {
      ElMessage.info(`${params.name}: ¥${formatNumber(params.data.value)}`);
    }
  });

  window.addEventListener("resize", () => myChart.resize());
}

// 初始化收款情况饼图
function initReceiptPieChart() {
  const chartDom = document.getElementById("receiptPieChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 处理收款数据
  let chartData = [];
  if (receiptData.value.length > 1) {
    // 跳过表头和合计行，处理数据行
    const dataRows = receiptData.value.slice(1);

    dataRows.forEach((row, index) => {
      const name = row[0]; // 收款类型
      const amountStr = row[1]; // 金额字符串

      // 跳过合计行
      if (name && name !== '合计' && amountStr) {
        const value = parseAmountString(amountStr);
        if (value > 0) {
          chartData.push({
            name: name,
            value: value,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: `hsl(${120 + index * 40}, 70%, 60%)` },
                { offset: 1, color: `hsl(${120 + index * 40}, 70%, 80%)` }
              ])
            }
          });
        }
      }
    });
  }

  if (chartData.length === 0) {
    chartData = [{ value: 0, name: '暂无数据', itemStyle: { color: '#ddd' } }];
  }

  const option = {
    title: {
      text: '收款构成',
      left: 'center',
      top: '5%',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: function(params) {
        return `💰 ${params.name}<br/>金额: ¥${formatNumber(params.value)}<br/>占比: ${params.percent}%`;
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: '5%',
      top: '20%',
      bottom: '5%',
      data: chartData.map(item => item.name),
      textStyle: {
        color: '#333',
        fontSize: 10
      },
      pageIconColor: '#409EFF',
      pageIconInactiveColor: '#C0C4CC'
    },
    series: [
      {
        name: '收款构成',
        type: 'pie',
        radius: ['25%', '65%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 2,
          shadowBlur: 8,
          shadowColor: 'rgba(0, 0, 0, 0.15)'
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 9,
          fontWeight: 'bold',
          formatter: function(params) {
            if (params.percent < 5) {
              return `${params.name}`;
            }
            return `${params.name}\n¥${formatNumber(params.value)}`;
          },
          rich: {
            name: {
              fontSize: 9,
              fontWeight: 'bold',
              color: '#333'
            },
            value: {
              fontSize: 8,
              color: '#666'
            }
          }
        },
        labelLine: {
          show: true,
          length: 12,
          length2: 8,
          smooth: true,
          lineStyle: {
            width: 1.5
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          },
          label: {
            show: true,
            fontSize: 11,
            fontWeight: 'bold'
          },
          scaleSize: 3
        },
        data: chartData,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return idx * 100;
        }
      }
    ]
  };

  myChart.setOption(option);

  // 添加点击事件
  myChart.on('click', function(params) {
    if (params.data && params.data.value > 0) {
      ElMessage.info(`${params.name}: ¥${formatNumber(params.data.value)}`);
    }
    myChart.resize();
  });
}

// 初始化付款情况饼图
function initPaymentPieChart() {
  const chartDom = document.getElementById("paymentPieChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 处理付款数据 - 从二维表格中提取数据
  let chartData = [];
  if (paymentPivotData.value.length > 1) {
    // 跳过表头，处理数据行
    const dataRows = paymentPivotData.value.slice(1);

    // 找到合计行的索引（通常是最后一行）
    const totalRowIndex = dataRows.findIndex(row =>
      row[0] === '合计' || row[0] === '总付款' || row[0] === '小计'
    );

    // 如果有合计行，排除它
    const validRows = totalRowIndex >= 0 ? dataRows.slice(0, totalRowIndex) : dataRows;

    validRows.forEach((row, index) => {
      const name = row[0]; // 付款类型

      // 计算该行的总金额（排除第一列的名称列）
      let totalAmount = 0;
      for (let i = 1; i < row.length; i++) {
        const cellValue = row[i];
        if (cellValue && typeof cellValue === 'string') {
          const amount = parseAmountString(cellValue);
          totalAmount += amount;
        } else if (typeof cellValue === 'number') {
          totalAmount += cellValue;
        }
      }

      // 只添加有效的数据项
      if (name && totalAmount > 0) {
        chartData.push({
          name: name,
          value: totalAmount,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: `hsl(${15 + index * 45}, 75%, 55%)` },
              { offset: 1, color: `hsl(${15 + index * 45}, 75%, 75%)` }
            ])
          }
        });
      }
    });
  }

  if (chartData.length === 0) {
    chartData = [{ value: 0, name: '暂无数据', itemStyle: { color: '#ddd' } }];
  }

  const option = {
    title: {
      text: '付款构成',
      left: 'center',
      top: '5%',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: function(params) {
        return `💰 ${params.name}<br/>金额: ¥${formatNumber(params.value)}<br/>占比: ${params.percent}%`;
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: '5%',
      top: '20%',
      bottom: '5%',
      data: chartData.map(item => item.name),
      textStyle: {
        color: '#333',
        fontSize: 10
      },
      pageIconColor: '#409EFF',
      pageIconInactiveColor: '#C0C4CC'
    },
    series: [
      {
        name: '付款构成',
        type: 'pie',
        radius: ['25%', '65%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 2,
          shadowBlur: 8,
          shadowColor: 'rgba(0, 0, 0, 0.15)'
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 9,
          fontWeight: 'bold',
          formatter: function(params) {
            if (params.percent < 5) {
              return `${params.name}`;
            }
            return `${params.name}\n¥${formatNumber(params.value)}`;
          },
          rich: {
            name: {
              fontSize: 9,
              fontWeight: 'bold',
              color: '#333'
            },
            value: {
              fontSize: 8,
              color: '#666'
            }
          }
        },
        labelLine: {
          show: true,
          length: 12,
          length2: 8,
          smooth: true,
          lineStyle: {
            width: 1.5
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          },
          label: {
            show: true,
            fontSize: 11,
            fontWeight: 'bold'
          },
          scaleSize: 3
        },
        data: chartData,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return idx * 100;
        }
      }
    ]
  };

  myChart.setOption(option);

  // 添加点击事件
  myChart.on('click', function(params) {
    if (params.data && params.data.value > 0) {
      ElMessage.info(`${params.name}: ¥${formatNumber(params.data.value)}`);
    }
  });

  window.addEventListener("resize", () => myChart.resize());
}

// 新增：初始化“其他资金付款情况”柱状图
function initOtherPaymentPieChart() {
  const chartDom = document.getElementById("otherPaymentPieChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 处理otherPaymentData，假设数据结构为二维数组，第一行为表头
  let categories = [];
  let values = [];
  if (otherPaymentData.value.length > 1) {
    const dataRows = otherPaymentData.value.slice(1);
    dataRows.forEach(row => {
      const name = row[0];
      const amountStr = row[1];
      if (name && name !== '合计' && amountStr) {
        const value = parseAmountString(amountStr);
        categories.push(name);
        values.push(value);
      }
    });
  }

  if (categories.length === 0) {
    categories = ['暂无数据'];
    values = [0];
  }

  const option = {
    title: {
      text: '其他资金流入/流出',
      left: 'center',
      top: '5%',
      textStyle: {
        fontSize: 15,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function(params) {
        const value = params[0].value;
        const type = value >= 0 ? '流入' : '流出';
        return `${params[0].name}<br/>类型: ${type}<br/>金额: ¥${formatNumber(Math.abs(value))}`;
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '12%',
      top: '22%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: { rotate: 30, fontSize: 10 }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return '¥' + formatNumber(value);
        }
      }
    },
    series: [
      {
        name: '金额',
        type: 'bar',
        data: values,
        itemStyle: {
          color: function(params) {
            return params.value >= 0
              ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#67C23A' },
                  { offset: 1, color: '#95D475' }
                ])
              : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#F56C6C' },
                  { offset: 1, color: '#F9A7A7' }
                ]);
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: function(params) {
            return (params.value >= 0 ? '+' : '-') + formatNumber(Math.abs(params.value));
          },
          fontSize: 10
        }
      }
    ]
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}

// 初始化在途付款分组统计图
function initPendingSummaryChart() {
  const chartDom = document.getElementById("pendingSummaryChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 按付款类型分组统计
  const typeStats = {};
  pendingPaymentData.value.slice(1).forEach(row => {
    const type = row[2]; // 付款类型
    const amount = row[11]; // 申请金额
    if (typeStats[type]) {
      typeStats[type] += amount;
    } else {
      typeStats[type] = amount;
    }
  });

  const categories = Object.keys(typeStats);
  const values = Object.values(typeStats);

  const option = {
    title: {
      text: '在途付款分组统计',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        return `${params[0].name}<br/>金额: ¥${formatNumber(params[0].value)}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return '¥' + (value / 10000).toFixed(0) + '万';
        }
      }
    },
    series: [
      {
        name: '付款金额',
        type: 'bar',
        data: values,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#0a5cad' }
          ])
        },
        emphasis: {
          focus: 'series'
        }
      }
    ]
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}

// 图表切换函数
function switchChart() {
  switch (chartType.value) {
    case 'pie':
      initFundDistributionChart();
      break;
    case 'waterfall':
      initWaterfallChart();
      break;
    case 'sankey':
      initSankeyChart();
      break;
  }
}

// 初始化瀑布图
function initWaterfallChart() {
  const chartDom = document.getElementById("waterfallChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 处理瀑布图数据
  let categories = ['期初余额'];
  let data = [{ value: 0, itemStyle: { color: '#91cc75' } }]; // 期初余额
  let total = 0;

  if (fundDistributionData.value.length > 0) {
    fundDistributionData.value.forEach(item => {
      categories.push(item.name);
      total += item.value;

      if (item.value > 0) {
        // 流入 - 绿色
        data.push({
          value: item.value,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#67C23A' },
              { offset: 1, color: '#85CE61' }
            ])
          }
        });
      } else {
        // 流出 - 红色
        data.push({
          value: item.value,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#F56C6C' },
              { offset: 1, color: '#F78989' }
            ])
          }
        });
      }
    });
  }

  categories.push('期末余额');
  data.push({
    value: total,
    itemStyle: {
      color: total >= 0 ? '#409EFF' : '#E6A23C'
    }
  });

  const option = {
    title: {
      text: '资金流动瀑布图',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      textStyle: {
        color: '#fff'
      },
      formatter: function(params) {
        const param = params[0];
        const value = param.value;
        const isInflow = value > 0;
        const arrow = isInflow ? '↗️' : '↘️';
        const type = isInflow ? '流入' : '流出';

        if (param.name === '期初余额' || param.name === '期末余额') {
          return `${param.name}<br/>金额: ¥${formatNumber(Math.abs(value))}`;
        }

        return `${arrow} ${type}<br/>${param.name}<br/>金额: ¥${formatNumber(Math.abs(value))}`;
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        rotate: 45,
        fontSize: 11
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '金额（元）',
      axisLabel: {
        formatter: function(value) {
          return '¥' + formatNumber(value);
        }
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        name: '资金流动',
        type: 'bar',
        barWidth: '60%',
        data: data,
        label: {
          show: true,
          position: 'top',
          formatter: function(params) {
            const value = params.value;
            const sign = value >= 0 ? '+' : '';
            return sign + formatNumber(value);
          },
          fontSize: 10,
          fontWeight: 'bold'
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        animationDelay: function (idx) {
          return idx * 100;
        }
      }
    ],
    animationEasing: 'elasticOut',
    animationDelayUpdate: function (idx) {
      return idx * 50;
    }
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}

// 初始化桑基图
function initSankeyChart() {
  const chartDom = document.getElementById("sankeyChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 构建桑基图数据
  const nodes = [
    { name: '资金池', itemStyle: { color: '#409EFF' } }
  ];
  const links = [];

  if (fundDistributionData.value.length > 0) {
    fundDistributionData.value.forEach(item => {
      if (item.value !== 0) {
        nodes.push({
          name: item.name,
          itemStyle: {
            color: item.value > 0 ? '#67C23A' : '#F56C6C'
          }
        });

        if (item.value > 0) {
          // 流入：从来源到资金池
          links.push({
            source: item.name,
            target: '资金池',
            value: item.value,
            lineStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#67C23A' },
                { offset: 1, color: '#85CE61' }
              ])
            }
          });
        } else {
          // 流出：从资金池到去向
          links.push({
            source: '资金池',
            target: item.name,
            value: Math.abs(item.value),
            lineStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#F56C6C' },
                { offset: 1, color: '#F78989' }
              ])
            }
          });
        }
      }
    });
  }

  const option = {
    title: {
      text: '资金流动桑基图',
      left: 'center',
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      textStyle: {
        color: '#fff'
      },
      formatter: function(params) {
        if (params.dataType === 'edge') {
          const isInflow = params.data.source !== '资金池';
          const arrow = isInflow ? '→' : '←';
          const type = isInflow ? '流入' : '流出';
          return `${type} ${arrow}<br/>${params.data.source} → ${params.data.target}<br/>金额: ¥${formatNumber(params.data.value)}`;
        } else {
          return `${params.name}<br/>节点`;
        }
      }
    },
    series: [
      {
        type: 'sankey',
        layout: 'none',
        emphasis: {
          focus: 'adjacency'
        },
        nodeAlign: 'left',
        nodeGap: 20,
        nodeWidth: 30,
        layoutIterations: 0,
        data: nodes,
        links: links,
        lineStyle: {
          opacity: 0.8,
          curveness: 0.3
        },
        label: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }
    ]
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}

// 页面加载时初始化
onMounted(() => {
  // 使用 requestAnimationFrame 确保在下一帧渲染时执行滚动重置
  requestAnimationFrame(() => {
    // 只重置容器滚动位置，避免全局滚动导致的跳动
    const container = document.querySelector('.capital-flow-container');
    if (container) {
      container.scrollTop = 0;
    }
  });
  
  // 页面加载时自动获取数据
  fetchCapitalFlowData();
});
</script>

<style scoped>
.capital-flow-container {
  width: 100%;
  height: 100%;
  padding: 24px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  overflow-x: hidden;
  /* 禁用平滑滚动以避免跳动效果 */
  scroll-behavior: auto;
}

h1 {
  margin-top: 0;
  margin-bottom: 32px;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

/* 时间选择器区域 */
.time-selector-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.date-range-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-separator {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin: 0 8px;
}

.quick-select-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

/* 区域标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-tabs {
  display: flex;
  align-items: center;
}

.section-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  position: relative;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 2px;
}

/* 各个功能区域 */
.fund-status-section,
.receipt-section,
.payment-section,
.chart-section,
.pending-payment-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #fafbfc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.fund-status-table {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

/* 收款情况和付款情况的布局 */
.receipt-content,
.payment-content {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.receipt-table,
.payment-table {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.receipt-chart,
.payment-chart {
  flex: 0 0 350px;
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.chart-small {
  height: 280px;
  width: 100%;
}

/* 在途付款区域特殊样式 */
.detail-tabs {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.chart {
  height: 400px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f2f5;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 无数据状态 */
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
  font-size: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .capital-flow-container {
    padding: 16px;
  }

  .time-selector-section {
    padding: 16px;
  }

  .date-range-container {
    flex-direction: column;
    gap: 8px;
  }

  .quick-select-buttons {
    justify-content: center;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-buttons .el-button {
    width: 100%;
  }

  .fund-status-section,
  .receipt-section,
  .payment-section,
  .chart-section,
  .detail-section {
    padding: 16px;
    margin-bottom: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart {
    height: 300px;
  }

  /* 响应式布局调整 */
  .receipt-content,
  .payment-content {
    flex-direction: column;
    gap: 16px;
  }

  .receipt-chart,
  .payment-chart {
    flex: none;
    padding: 8px;
  }

  .chart-small {
    height: 240px;
  }
}

/* 动画效果 */
.fund-status-section,
.receipt-section,
.payment-section,
.chart-section,
.pending-payment-section {
  transition: all 0.3s ease;
}

.fund-status-section:hover,
.receipt-section:hover,
.payment-section:hover,
.chart-section:hover,
.detail-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}
</style>
