<template>
  <div class="universal-table-example">
    <div class="example-header">
      <h2>通用表格组件使用示例</h2>
      <div class="example-controls">
        <button @click="loadSampleData" class="example-button">
          加载示例数据
        </button>
        <button @click="clearData" class="example-button secondary">
          清空数据
        </button>
        <button @click="showCurrentData" class="example-button info">
          查看当前数据
        </button>
        <button @click="downloadCSV" class="example-button success">
          下载 CSV
        </button>
        <button @click="showStatistics" class="example-button warning">
          查看统计
        </button>
        <button @click="cleanData" class="example-button secondary">
          清理数据
        </button>
      </div>
    </div>

    <!-- 通用表格组件 -->
    <UniversalTableComponent
      ref="tableComponent"
      :data-provider="dataProvider"
      :initial-data="initialData"
      workbook-name="示例工作簿"
      @data-change="handleDataChange"
      @initialized="handleInitialized"
      @error="handleError"
    />

    <!-- 数据展示区域 -->
    <div v-if="showDataPreview" class="data-preview">
      <h3>当前表格数据：</h3>
      <pre>{{ JSON.stringify(currentTableData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import UniversalTableComponent from '../components/UniversalTableComponent.vue'
import type { TableData } from '../types/UniversalTable'
import { 
  downloadTableDataAsCSV, 
  getTableStatistics, 
  cleanTableData,
  validateIdNumber 
} from '../utils/tableUtils'

// 响应式数据
const tableComponent = ref<InstanceType<typeof UniversalTableComponent> | null>(null)
const currentTableData = ref<TableData>({})
const showDataPreview = ref(false)
const statistics = ref<any>(null)

// 初始数据 - 包含多个表格
const initialData = reactive<TableData>({
  '员工信息表': [
    ['工号', '姓名', '身份证号', '部门', '职位', '入职日期'],
    ['E001', '张三', '110101199001011234', '技术部', '高级工程师', '2023-01-15'],
    ['E002', '李四', '110101199102022345', '财务部', '会计师', '2023-02-20'],
    ['E003', '王五', '110101199203033456', '人事部', '人事专员', '2023-03-10']
  ],
  '薪资表': [
    ['工号', '姓名', '基本工资', '绩效奖金', '津贴', '扣款', '实发工资'],
    ['E001', '张三', 15000, 3000, 1000, 500, 18500],
    ['E002', '李四', 12000, 2000, 800, 300, 14500],
    ['E003', '王五', 10000, 1500, 600, 200, 11900]
  ],
  '考勤表': [
    ['工号', '姓名', '出勤天数', '迟到次数', '早退次数', '请假天数', '加班小时'],
    ['E001', '张三', 22, 0, 0, 0, 10],
    ['E002', '李四', 21, 1, 0, 1, 5],
    ['E003', '王五', 22, 0, 1, 0, 8]
  ]
})

// 数据提供函数 - 模拟从后端获取数据
const dataProvider = async (): Promise<TableData> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 模拟从后端获取的数据
  return {
    '员工信息表': [
      ['工号', '姓名', '身份证号', '部门', '职位', '入职日期', '状态'],
      ['E001', '张三', '110101199001011234', '技术部', '高级工程师', '2023-01-15', '在职'],
      ['E002', '李四', '110101199102022345', '财务部', '会计师', '2023-02-20', '在职'],
      ['E003', '王五', '110101199203033456', '人事部', '人事专员', '2023-03-10', '在职'],
      ['E004', '赵六', '110101199404044567', '市场部', '市场经理', '2023-04-01', '在职']
    ],
    '薪资表': [
      ['工号', '姓名', '基本工资', '绩效奖金', '津贴', '扣款', '实发工资', '发放日期'],
      ['E001', '张三', 15000, 3500, 1200, 400, 19300, '2024-01-31'],
      ['E002', '李四', 12000, 2200, 900, 250, 14850, '2024-01-31'],
      ['E003', '王五', 10000, 1800, 700, 150, 12350, '2024-01-31'],
      ['E004', '赵六', 18000, 4000, 1500, 600, 22900, '2024-01-31']
    ],
    '考勤表': [
      ['工号', '姓名', '出勤天数', '迟到次数', '早退次数', '请假天数', '加班小时', '月份'],
      ['E001', '张三', 22, 0, 0, 0, 12, '2024-01'],
      ['E002', '李四', 21, 1, 0, 1, 6, '2024-01'],
      ['E003', '王五', 22, 0, 1, 0, 9, '2024-01'],
      ['E004', '赵六', 20, 2, 1, 2, 15, '2024-01']
    ],
    '新增报表': [
      ['项目编号', '项目名称', '负责人', '开始日期', '结束日期', '状态'],
      ['P001', '系统升级项目', '张三', '2024-01-01', '2024-03-31', '进行中'],
      ['P002', '数据迁移项目', '李四', '2024-02-01', '2024-04-30', '计划中']
    ]
  }
}

// 加载示例数据
const loadSampleData = () => {
  if (tableComponent.value) {
    tableComponent.value.loadData(initialData)
  }
}

// 清空数据
const clearData = () => {
  if (tableComponent.value) {
    tableComponent.value.loadData({})
  }
}

// 查看当前数据
const showCurrentData = () => {
  if (tableComponent.value) {
    const data = tableComponent.value.getAllTableData()
    currentTableData.value = data
    showDataPreview.value = !showDataPreview.value
  }
}

// 下载 CSV
const downloadCSV = () => {
  if (tableComponent.value) {
    const data = tableComponent.value.getAllTableData()
    downloadTableDataAsCSV(data, 'table-export.csv')
  }
}

// 查看统计信息
const showStatistics = () => {
  if (tableComponent.value) {
    const data = tableComponent.value.getAllTableData()
    statistics.value = getTableStatistics(data)
    console.log('表格统计信息:', statistics.value)
    alert(`统计信息：
总表格数: ${statistics.value.totalTables}
总行数: ${statistics.value.totalRows}
总单元格数: ${statistics.value.totalCells}
空表格: ${statistics.value.emptyTables.join(', ') || '无'}
最大表格: ${statistics.value.largestTable.name} (${statistics.value.largestTable.rows}行 × ${statistics.value.largestTable.cols}列)`)
  }
}

// 清理数据
const cleanData = () => {
  if (tableComponent.value) {
    const data = tableComponent.value.getAllTableData()
    const cleanedData = cleanTableData(data)
    tableComponent.value.loadData(cleanedData)
    alert('数据清理完成！已移除空行和空列。')
  }
}

// 事件处理函数
const handleDataChange = (data: TableData) => {
  console.log('表格数据发生变化:', data)
  currentTableData.value = data
  
  // 验证身份证号
  Object.entries(data).forEach(([tableName, tableData]) => {
    if (tableData.length > 0) {
      const headerRow = tableData[0]
      const idColumnIndex = headerRow.findIndex(header => 
        typeof header === 'string' && header.includes('身份证号')
      )
      
      if (idColumnIndex !== -1) {
        for (let i = 1; i < tableData.length; i++) {
          const idNumber = tableData[i][idColumnIndex]
          if (idNumber && typeof idNumber === 'string' && !validateIdNumber(idNumber)) {
            console.warn(`表格 ${tableName} 第 ${i + 1} 行身份证号格式可能有误: ${idNumber}`)
          }
        }
      }
    }
  })
  
  // 这里可以将数据发送到后端保存
  // await saveDataToBackend(data)
}

const handleInitialized = () => {
  console.log('表格组件初始化完成')
}

const handleError = (error: string) => {
  console.error('表格组件错误:', error)
  alert(`错误: ${error}`)
}
</script>

<style scoped>
.universal-table-example {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.example-header {
  background: white;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.example-header h2 {
  margin: 0;
  color: #333;
}

.example-controls {
  display: flex;
  gap: 12px;
}

.example-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.example-button {
  background: #4CAF50;
  color: white;
}

.example-button:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.example-button.secondary {
  background: #f44336;
  color: white;
}

.example-button.secondary:hover {
  background: #da190b;
}

.example-button.info {
  background: #2196F3;
  color: white;
}

.example-button.info:hover {
  background: #1976D2;
}

.example-button.success {
  background: #4CAF50;
  color: white;
}

.example-button.success:hover {
  background: #45a049;
}

.example-button.warning {
  background: #FF9800;
  color: white;
}

.example-button.warning:hover {
  background: #F57C00;
}

.data-preview {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 20px;
  max-width: 80vw;
  max-height: 80vh;
  overflow: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.data-preview h3 {
  margin-top: 0;
  color: #333;
}

.data-preview pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  font-size: 12px;
  max-height: 400px;
}
</style>