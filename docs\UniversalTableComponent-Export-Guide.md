# UniversalTableComponent 导出功能使用指南

## 概述

UniversalTableComponent 现在支持使用 ExcelJS 库进行数据导出，可以将表格数据导出为 Excel 文件(.xlsx格式)。

## 功能特性

- ✅ 支持多工作表导出
- ✅ 自动处理 Univer 单元格对象格式
- ✅ 自定义文件名
- ✅ 表头样式设置
- ✅ 自动列宽调整
- ✅ 边框样式
- ✅ 选择性工作表导出
- ✅ 错误处理和加载状态

## 基础用法

### 1. 基础导出

```javascript
// 在父组件中调用
await tableRef.value.exportData()
```

这将导出所有工作表数据，使用默认样式和自动生成的文件名。

### 2. 高级导出

```javascript
const options = {
  filename: '自定义文件名',
  includeSheets: ['工作表1', '工作表2'], // 可选，指定要导出的工作表
  headerStyle: {
    font: { bold: true, color: { argb: 'FFFFFFFF' } },
    fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
    alignment: { horizontal: 'center', vertical: 'middle' }
  },
  addBorders: true,
  autoFitColumns: true,
  maxColumnWidth: 50,
  minColumnWidth: 10
}

const result = await tableRef.value.exportDataWithOptions(options)
if (result.success) {
  console.log(`导出成功: ${result.filename}`)
}
```

## 导出选项说明

### exportDataWithOptions 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `filename` | String | 自动生成 | 导出文件名（不需要包含.xlsx扩展名） |
| `includeSheets` | Array\|null | null | 指定要导出的工作表名称数组，null表示导出所有 |
| `headerStyle` | Object | 默认样式 | 表头样式配置 |
| `addBorders` | Boolean | true | 是否添加单元格边框 |
| `autoFitColumns` | Boolean | true | 是否自动调整列宽 |
| `maxColumnWidth` | Number | 50 | 最大列宽 |
| `minColumnWidth` | Number | 10 | 最小列宽 |

### headerStyle 配置

```javascript
headerStyle: {
  font: {
    bold: true,           // 粗体
    italic: false,        // 斜体
    size: 12,            // 字体大小
    color: { argb: 'FF000000' }  // 字体颜色
  },
  fill: {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFF0F0F0' }  // 背景色
  },
  alignment: {
    horizontal: 'center', // 水平对齐: left, center, right
    vertical: 'middle'    // 垂直对齐: top, middle, bottom
  }
}
```

## 使用示例

### 完整的 Vue 组件示例

```vue
<template>
  <div>
    <div class="controls">
      <button @click="basicExport">基础导出</button>
      <button @click="customExport">自定义导出</button>
      <button @click="selectiveExport">选择性导出</button>
    </div>
    
    <UniversalTableComponent
      ref="tableRef"
      :initial-data="tableData"
      workbook-name="我的表格"
      @error="handleError"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import UniversalTableComponent from './UniversalTableComponent.vue'

const tableRef = ref(null)

const tableData = {
  '员工信息': [
    ['姓名', '工号', '部门'],
    ['张三', '001', '技术部'],
    ['李四', '002', '产品部']
  ]
}

// 基础导出
const basicExport = async () => {
  await tableRef.value.exportData()
}

// 自定义导出
const customExport = async () => {
  const options = {
    filename: `员工信息_${new Date().toLocaleDateString()}`,
    headerStyle: {
      font: { bold: true, color: { argb: 'FFFFFFFF' } },
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } }
    }
  }
  
  const result = await tableRef.value.exportDataWithOptions(options)
  if (result.success) {
    console.log('导出成功!')
  }
}

// 选择性导出
const selectiveExport = async () => {
  const options = {
    filename: '部分数据导出',
    includeSheets: ['员工信息'] // 只导出员工信息工作表
  }
  
  await tableRef.value.exportDataWithOptions(options)
}

const handleError = (error) => {
  console.error('导出错误:', error)
}
</script>
```

## 错误处理

导出功能包含完整的错误处理机制：

- 数据验证：检查是否有可导出的数据
- 文件生成错误：处理 ExcelJS 相关错误
- 下载错误：处理浏览器下载相关问题

错误会通过 `@error` 事件传递给父组件。

## 注意事项

1. **浏览器兼容性**：需要现代浏览器支持 Blob 和 URL.createObjectURL API
2. **文件大小**：大量数据可能影响导出性能，建议分批导出
3. **内存管理**：导出完成后会自动清理 URL 对象，避免内存泄漏
4. **文件名**：自动添加时间戳避免文件名冲突
5. **数据格式**：自动处理 Univer 单元格对象格式转换

## 依赖

- ExcelJS: ^4.4.0 (已在 package.json 中包含)

## 更新日志

- v1.0.0: 初始版本，支持基础导出功能
- v1.1.0: 添加高级导出选项和自定义样式支持