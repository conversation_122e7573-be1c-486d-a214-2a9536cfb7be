# 司库在途支付导出功能

## 功能概述

在CapitalFlowView组件中新增了司库在途支付导出功能，允许用户根据日期范围导出司库在途支付数据到Excel文件。

## 功能特点

1. **日期范围选择**: 提供开始日期和结束日期选择器
2. **Excel导出**: 支持将数据导出为Excel格式文件
3. **数据过滤**: 根据选择的日期范围过滤支付数据
4. **用户友好**: 提供加载状态和错误提示

## 界面布局

### 司库在途支付导出区域
- 位置：操作按钮行中，初始化台账区域下方
- 包含元素：
  - 导出开始日期选择器
  - 导出结束日期选择器  
  - 司库在途支付导出按钮

### 样式特点
- 使用浅蓝色背景 (#f0f8ff) 区分于初始化台账区域
- 响应式设计，支持移动端显示
- 与现有UI风格保持一致

## 技术实现

### 前端实现 (CapitalFlowView.vue)

#### 新增响应式数据
```javascript
// 司库在途支付导出日期选择
const exportStartDate = ref('')
const exportEndDate = ref('')

// 导出加载状态
const isExporting = ref(false)
```

#### 导出函数
```javascript
const exportTreasuryPayment = async () => {
  // 日期验证
  // API调用
  // 文件下载处理
  // 错误处理
}
```

### 后端实现 (capital_flow_api.py)

#### 新增API接口
- **路径**: `/api/treasury-payment-export`
- **方法**: POST
- **请求参数**: 
  ```json
  {
    "start_date": "2024-05-01",
    "end_date": "2024-05-31"
  }
  ```

#### 响应处理
- 成功：返回Excel文件流下载
- 失败：返回JSON错误信息

#### 数据结构
导出的Excel文件包含以下字段：
- 支付单号
- 申请日期
- 供应商名称
- 项目名称
- 合同编号
- 申请金额
- 币种
- 支付方式
- 审批状态
- 申请人
- 申请部门
- 预计支付日期
- 备注

## 依赖更新

### 后端依赖
在 `backend/requirements.txt` 中新增：
```
openpyxl==3.1.2
```

## 使用方法

1. 在CapitalFlowView页面中找到"司库在途支付导出区域"
2. 选择导出开始日期和结束日期
3. 点击"司库在途支付导出"按钮
4. 系统会根据日期范围过滤数据并生成Excel文件
5. 浏览器会自动下载生成的Excel文件

## 错误处理

- 未选择日期：显示警告提示"请选择导出开始日期和结束日期"
- 日期范围错误：显示警告提示"开始日期不能晚于结束日期"
- API调用失败：显示错误提示具体错误信息
- 网络错误：显示连接失败提示

## 文件命名规则

导出的Excel文件命名格式：
```
司库在途支付_开始日期_结束日期.xlsx
```

例如：`司库在途支付_2024-05-01_2024-05-31.xlsx`

## 注意事项

1. 目前使用模拟数据，实际部署时需要连接真实数据库
2. 文件下载依赖浏览器支持，建议使用现代浏览器
3. 大量数据导出时可能需要较长时间，请耐心等待
4. 导出功能需要后端服务正常运行

## 后续优化建议

1. 添加数据量限制和分页导出功能
2. 支持更多导出格式（CSV、PDF等）
3. 添加导出进度显示
4. 支持自定义字段选择导出
5. 添加导出历史记录功能