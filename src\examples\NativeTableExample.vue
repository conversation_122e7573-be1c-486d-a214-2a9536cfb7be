<template>
  <div class="native-table-example">
    <h1>原生表格组件使用示例</h1>
    
    <div class="example-section">
      <h2>基础用法</h2>
      <p>这是一个完全原生实现的表格组件，API与VTableComponent完全兼容。</p>
      
      <!-- 原生表格组件 -->
      <NativeTableComponent
        ref="tableRef"
        :data="tableData"
        :width="800"
        :height="400"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :auto-width="true"
        :enable-excel-import="true"
        @data-change="handleDataChange"
        @cell-edit="handleCellEdit"
        @copy="handleCopy"
        @paste="handlePaste"
      />
    </div>
    
    <div class="example-section">
      <h2>替换说明</h2>
      <div class="replacement-guide">
        <h3>1. 导入组件</h3>
        <pre><code>// 原来的导入
import VTableComponent from '@/components/VTableComponent.vue'

// 替换为
import NativeTableComponent from '@/components/NativeTableComponent.vue'</code></pre>
        
        <h3>2. 模板中使用</h3>
        <pre><code><!-- 原来的使用 -->
&lt;VTableComponent
  :data="tableData"
  :width="800"
  :height="400"
  :editable="true"
  @data-change="handleDataChange"
/&gt;

<!-- 替换为（API完全相同） -->
&lt;NativeTableComponent
  :data="tableData"
  :width="800"
  :height="400"
  :editable="true"
  @data-change="handleDataChange"
/&gt;</code></pre>
        
        <h3>3. 支持的Props</h3>
        <ul>
          <li><code>data</code> - 二维数组数据，第一行为标题</li>
          <li><code>width</code> - 表格宽度（默认600）</li>
          <li><code>height</code> - 表格高度（默认300）</li>
          <li><code>showFilter</code> - 是否显示筛选面板（默认true）</li>
          <li><code>editable</code> - 是否启用编辑功能（默认true）</li>
          <li><code>enableCopyPaste</code> - 是否启用复制粘贴（默认true）</li>
          <li><code>autoWidth</code> - 是否自动调整列宽（默认true）</li>
          <li><code>enableExcelImport</code> - 是否启用Excel导入（默认false）</li>
          <li><code>enablePushUpdate</code> - 是否启用后台推送更新（默认false）</li>
        </ul>
        
        <h3>4. 支持的事件</h3>
        <ul>
          <li><code>@data-change</code> - 数据变化时触发</li>
          <li><code>@cell-edit</code> - 单元格编辑时触发</li>
          <li><code>@copy</code> - 复制操作时触发</li>
          <li><code>@paste</code> - 粘贴操作时触发</li>
          <li><code>@cell-select</code> - 单元格选择时触发</li>
        </ul>
        
        <h3>5. 支持的方法</h3>
        <ul>
          <li><code>getData()</code> - 获取当前表格数据</li>
          <li><code>setData(newData)</code> - 设置表格数据</li>
          <li><code>addRow()</code> - 添加新行</li>
          <li><code>deleteRows()</code> - 删除选中行</li>
          <li><code>exportExcel()</code> - 导出Excel</li>
          <li><code>exportCSV()</code> - 导出CSV</li>
          <li><code>copy()</code> - 复制选中内容</li>
          <li><code>paste()</code> - 粘贴内容</li>
          <li><code>autoFitAllColumnWidth()</code> - 自动调整所有列宽</li>
        </ul>
      </div>
    </div>
    
    <div class="example-section">
      <h2>优势</h2>
      <div class="advantages">
        <div class="advantage-item">
          <h4>🚀 无外部依赖</h4>
          <p>不依赖@visactor/vtable等第三方库，减少包体积</p>
        </div>
        <div class="advantage-item">
          <h4>⚡ 性能优化</h4>
          <p>原生HTML表格，渲染性能更好，内存占用更少</p>
        </div>
        <div class="advantage-item">
          <h4>🔧 易于维护</h4>
          <p>代码完全可控，便于定制和维护</p>
        </div>
        <div class="advantage-item">
          <h4>📱 响应式设计</h4>
          <p>自动适应容器尺寸变化，支持移动端</p>
        </div>
        <div class="advantage-item">
          <h4>🎨 样式可定制</h4>
          <p>CSS样式完全可控，易于主题定制</p>
        </div>
        <div class="advantage-item">
          <h4>🔄 API兼容</h4>
          <p>与VTableComponent API完全兼容，无缝替换</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import NativeTableComponent from '../components/NativeTableComponent.vue'

const tableRef = ref()

// 示例数据
const tableData = ref([
  ['产品名称', '价格', '库存', '分类', '上架日期'],
  ['iPhone 15', 7999, 50, '手机', '2023-09-15'],
  ['MacBook Pro', 15999, 20, '电脑', '2023-10-01'],
  ['iPad Air', 4599, 30, '平板', '2023-08-20'],
  ['Apple Watch', 2999, 100, '手表', '2023-07-10'],
  ['AirPods Pro', 1899, 80, '耳机', '2023-06-05']
])

// 事件处理
const handleDataChange = (newData) => {
  console.log('数据变化:', newData)
  tableData.value = newData
}

const handleCellEdit = (event) => {
  console.log('单元格编辑:', event)
}

const handleCopy = (event) => {
  console.log('复制操作:', event)
}

const handlePaste = (event) => {
  console.log('粘贴操作:', event)
}
</script>

<style scoped>
.native-table-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.example-section {
  margin-bottom: 40px;
}

h1 {
  color: #2c3e50;
  margin-bottom: 30px;
  text-align: center;
}

h2 {
  color: #34495e;
  margin-bottom: 20px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

h3 {
  color: #2980b9;
  margin: 20px 0 10px 0;
}

h4 {
  color: #27ae60;
  margin: 10px 0 5px 0;
}

.replacement-guide {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.replacement-guide pre {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 10px 0;
}

.replacement-guide code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.replacement-guide ul {
  margin: 10px 0;
  padding-left: 20px;
}

.replacement-guide li {
  margin: 5px 0;
  line-height: 1.6;
}

.replacement-guide li code {
  background: #e8f4f8;
  color: #2980b9;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 13px;
}

.advantages {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.advantage-item {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #27ae60;
}

.advantage-item h4 {
  margin-top: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.advantage-item p {
  color: #7f8c8d;
  line-height: 1.6;
  margin: 10px 0 0 0;
}

p {
  line-height: 1.6;
  color: #555;
}
</style>
