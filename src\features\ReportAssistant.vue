<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon><ArrowLeft /></el-icon>
        返回主页
      </el-button>
      <h1 class="title">报表助手</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <el-tooltip
          v-for="module in modules"
          :key="module.key"
          :content="module.tooltip"
          placement="bottom"
          :show-after="300"
          effect="light"
          :popper-style="{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '13px',
            padding: '8px 12px',
            maxWidth: '200px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }"
        >
          <div
            :class="['nav-tab', { active: activeModule === module.key }]"
            @click="setActiveModule(module.key)"
          >
            <el-icon><component :is="module.icon" /></el-icon>
            <span>{{ module.title }}</span>
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import ProjectReportView from '../views/ProjectReportView.vue'
import SmartReconciliationView from '../views/SmartReconciliationView.vue'
import ProjectReportGeneratorView from '../views/ProjectReportGeneratorView.vue'
import ReportAssistantView from '../views/ReportAssistantView.vue'
import ReportAuditView from '../views/ReportAuditView.vue'


const router = useRouter()
const activeModule = ref('project-report')

const modules = [
  {
    key: 'project-report',
    title: '项目台账',
    icon: 'Folder',
    component: ProjectReportView,
    tooltip: '管理项目财务台账，跟踪项目收支和成本核算情况,需要先同步统计大师里面的明细账'
  },
  {
    key: 'project',
    title: '项目报表',
    icon: 'DataLine',
    component: ProjectReportGeneratorView,
    tooltip: '生成专业的项目财务报表，支持多维度数据分析和展示,需要先同步统计大师里面的明细账'
  },
  {
    key: 'offset',
    title: '对账抵消',
    icon: 'RefreshRight',
    component: SmartReconciliationView,
    tooltip: '智能对账和抵消处理，自动识别和处理往来账款抵消，通过导出报表系统的抵消文件再导入'
  },
  {
    key: 'report-assistant',
    title: '年报助手',
    icon: 'Tools',
    component: ReportAssistantView,
    tooltip: '协助完成年度财务报表编制，提供专业的报表制作工具'
  },
  {
    key: 'report-audit',
    title: '年报稽核',
    icon: 'DocumentChecked',
    component: ReportAuditView,
    tooltip: '年报稽核功能，支持多种类型的财务报表稽核分析'
  }
]

const currentComponent = computed(() => {
  const module = modules.find(m => m.key === activeModule.value)
  return module ? module.component : 'div'
})

const setActiveModule = (key) => {
  activeModule.value = key
}

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.statistics-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  width: 100%;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  overflow: hidden;
  background: #f5f7fa;
}

.content-area > * {
  height: 100%;
  overflow: auto;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 2px;
}
</style>