<template>
  <div class="base-query-component">
    <!-- 标题栏和返回按钮 -->
    <div class="header">
      <el-button 
        type="primary" 
        :icon="ArrowLeft" 
        @click="handleBack"
        class="back-button"
      >
        返回
      </el-button>
      <h1>{{ title }}</h1>
    </div>

    <!-- 查询面板 -->
    <div class="query-panel">
      <el-form 
        :model="queryForm" 
        :inline="true" 
        class="query-form"
        label-width="100px"
      >
        <!-- 动态渲染查询字段 -->
        <el-form-item 
          v-for="field in queryFields" 
          :key="field.key"
          :label="field.label"
        >
          <!-- 文本输入框 -->
          <el-input 
            v-if="field.type === 'text'"
            v-model="queryForm[field.key]" 
            :placeholder="field.placeholder"
            clearable
            :style="{ width: field.width || '200px' }"
          />
          
          <!-- 数字输入框 -->
          <el-input 
            v-else-if="field.type === 'number'"
            v-model="queryForm[field.key]" 
            :placeholder="field.placeholder"
            type="number"
            clearable
            :style="{ width: field.width || '150px' }"
          />
          
          <!-- 日期范围选择器 -->
          <el-date-picker
            v-else-if="field.type === 'daterange'"
            v-model="queryForm[field.key]"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :style="{ width: field.width || '240px' }"
          />
          
          <!-- 单日期选择器 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="queryForm[field.key]"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :style="{ width: field.width || '180px' }"
          />
          
          <!-- 下拉选择器 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="queryForm[field.key]"
            :placeholder="field.placeholder"
            clearable
            :style="{ width: field.width || '180px' }"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          
          <!-- 金额范围 -->
          <div v-else-if="field.type === 'amount-range'" class="amount-range">
            <el-input 
              v-model="queryForm[field.key + 'Min']" 
              placeholder="最小金额"
              type="number"
              style="width: 120px"
            />
            <span style="margin: 0 8px">-</span>
            <el-input 
              v-model="queryForm[field.key + 'Max']" 
              placeholder="最大金额"
              type="number"
              style="width: 120px"
            />
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            :icon="Search" 
            @click="handleQuery"
            :loading="loading"
          >
            查询
          </el-button>
          <el-button 
            :icon="Refresh" 
            @click="handleReset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 查询结果表格 -->
    <div class="result-panel">
      <div class="result-header">
        <span class="result-count">
          共找到 {{ tableData.length > 0 ? tableData.length - 1 : 0 }} 条记录
        </span>
        <el-button 
          v-if="tableData.length > 0"
          type="success" 
          :icon="Download" 
          size="small"
          @click="handleExport"
        >
          导出Excel
        </el-button>
      </div>
      
      <NativeTableComponent
        v-if="tableData.length > 0"
        :data="tableData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        class="query-table"
      />
      
      <div v-else-if="!loading && hasQueried" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      
      <div v-else-if="!hasQueried" class="no-query">
        <el-empty description="请输入查询条件进行查询" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Search, Refresh, Download } from '@element-plus/icons-vue'
import NativeTableComponent from '../components/NativeTableComponent.vue'
import axios from 'axios'

// 定义Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  queryFields: {
    type: Array,
    required: true
  },
  mockData: {
    type: Array,
    default: () => []
  },
  apiEndpoint: {
    type: String,
    default: ''
  }
})

// 定义事件
const emit = defineEmits(['back'])

// 响应式数据
const queryForm = ref({})
const loading = ref(false)
const hasQueried = ref(false)
const tableData = ref([])
const tableWidth = ref(1000)
const tableHeight = ref(500)

// 初始化查询表单
function initQueryForm() {
  const form = {}
  props.queryFields.forEach(field => {
    if (field.type === 'amount-range') {
      form[field.key + 'Min'] = ''
      form[field.key + 'Max'] = ''
    } else {
      form[field.key] = field.type === 'daterange' ? null : ''
    }
  })
  queryForm.value = form
}

// 处理返回
function handleBack() {
  emit('back')
}

// 处理查询
async function handleQuery() {
  loading.value = true
  hasQueried.value = true

  try {
    if (props.apiEndpoint) {
      // 构造以 label 为键的过滤条件对象
      const labelFilters = {}
      props.queryFields.forEach(field => {
        if (field.type === 'amount-range') {
          labelFilters[`${field.label}Min`] = queryForm.value[field.key + 'Min']
          labelFilters[`${field.label}Max`] = queryForm.value[field.key + 'Max']
        } else {
          labelFilters[field.label] = queryForm.value[field.key]
        }
      })

      // 使用API调用
      const response = await axios.post(`http://localhost:8000${props.apiEndpoint}`, {
        filters: labelFilters,
        timestamp: new Date().toISOString()
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10秒超时
      })

      if (response.data && response.data.code === 200) {
        tableData.value = response.data.data || []
        ElMessage.success(`查询完成，共找到 ${tableData.value.length > 0 ? tableData.value.length - 1 : 0} 条记录`)
      } else {
        throw new Error(response.data?.message || '查询失败')
      }
    } else {
      // 使用模拟数据进行筛选
      let filteredData = [...props.mockData]

      // 简单的筛选逻辑（可以根据具体需求扩展）
      Object.keys(queryForm.value).forEach(key => {
        const value = queryForm.value[key]
        if (value && typeof value === 'string' && value.trim()) {
          // 对于文本字段，在数据中查找包含该文本的行
          filteredData = [
            filteredData[0], // 保留表头
            ...filteredData.slice(1).filter(row =>
              row.some(cell =>
                cell && cell.toString().toLowerCase().includes(value.toLowerCase())
              )
            )
          ]
        }
      })

      tableData.value = filteredData
      ElMessage.success(`查询完成，共找到 ${filteredData.length - 1} 条记录`)
    }
  } catch (error) {
    console.error('查询失败:', error)
    let errorMessage = '查询失败'
    if (error.response) {
      errorMessage += `，服务器返回：${error.response.status}`
    } else if (error.request) {
      errorMessage += '，无法连接到服务器'
    } else {
      errorMessage += `，${error.message}`
    }
    ElMessage.error(errorMessage)

    // 如果API调用失败且有模拟数据，则使用模拟数据
    if (props.mockData && props.mockData.length > 0) {
      tableData.value = props.mockData
      ElMessage.warning('API调用失败，已切换到模拟数据')
    }
  } finally {
    loading.value = false
  }
}

// 处理重置
function handleReset() {
  initQueryForm()
  tableData.value = []
  hasQueried.value = false
}

// 处理导出
function handleExport() {
  ElMessage.info('导出功能开发中...')
}

// 计算表格尺寸
function calculateTableSize() {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  
  tableWidth.value = Math.max(800, windowWidth - 100)
  tableHeight.value = Math.max(400, windowHeight - 400)
}

// 窗口大小变化处理
function handleResize() {
  calculateTableSize()
}

// 监听props变化，重新初始化表单
watch(() => props.queryFields, () => {
  initQueryForm()
}, { immediate: true })

// 组件挂载
onMounted(() => {
  calculateTableSize()
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.base-query-component {
  padding: 20px;
  background: #f8f9fb;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.back-button {
  flex-shrink: 0;
}

.query-panel {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

.query-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.amount-range {
  display: flex;
  align-items: center;
}

.result-panel {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.result-count {
  font-size: 14px;
  color: #666;
}

.query-table {
  margin-top: 16px;
}

.no-data,
.no-query {
  padding: 40px 0;
  text-align: center;
}
</style>
