<template>
    <div class="financial-comparison-container">
        <div class="comparison-header">
            <h2>财商数据比对</h2>
            <p class="description">先到统计大师哪里同步一体化合同合账</p>
        </div>

        <div class="action-section">
            <el-button type="success" size="large" @click="getFinancialDataDifference" :loading="loading"
                class="get-difference-btn">
                <el-icon class="play-icon">
                    <VideoPlay />
                </el-icon>
                {{ loading ? '获取中...' : '获取财商数据差额' }}
            </el-button>
        </div>

        <!-- 引入消息轮询对话框 -->
        <MessagePollingDialog v-model="dialogVisible" />
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const loading = ref(false)
const dialogVisible = ref(false)

// 获取财商数据差额
const getFinancialDataDifference = async () => {
    loading.value = true

    try {
        const response = await fetch('http://127.0.0.1:8000/api/start-function', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                '功能': '获取财商数据差额',
                '参数': []
            })
        })

        if (response.ok) {
            dialogVisible.value = true
            ElMessage.success('获取财商数据差额任务已启动')
        } else {
            ElMessage.error('获取财商数据差额失败')
        }
    } catch (error) {
        ElMessage.error('获取过程中发生错误: ' + error.message)
    } finally {
        loading.value = false
    }
}
</script>

<style scoped>
.financial-comparison-container {
    padding: 40px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 500px;
}

.comparison-header {
    text-align: center;
    margin-bottom: 40px;
}

.comparison-header h2 {
    color: #303133;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
}

.description {
    color: #606266;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.action-section {
    display: flex;
    justify-content: center;
    align-items: center;
}

.get-difference-btn {
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    min-width: 200px;
    height: auto;
    background: #67c23a;
    border-color: #67c23a;
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
    transition: all 0.3s ease;
}

.get-difference-btn:hover {
    background: #5daf34;
    border-color: #5daf34;
    box-shadow: 0 6px 16px rgba(103, 194, 58, 0.4);
    transform: translateY(-2px);
}

.get-difference-btn:active {
    transform: translateY(0);
}

.play-icon {
    margin-right: 8px;
    font-size: 18px;
}

/* 加载状态样式 */
.get-difference-btn.is-loading {
    background: #a0cfff;
    border-color: #a0cfff;
}
</style>