<template>
  <div class="table-test-component">
    <h2>表格测试 - 固定表头和滚动条</h2>
    
    <div class="test-controls">
      <button @click="loadTestData" class="test-btn">加载测试数据</button>
      <button @click="addMoreData" class="test-btn">添加更多数据</button>
      <button @click="clearData" class="test-btn">清空数据</button>
    </div>
    
    <div class="table-wrapper">
      <NativeTableComponent
        v-if="tableData.length > 0"
        :data="tableData"
        :width="800"
        :height="400"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="false"
        :enable-push-update="false"
        class="test-table"
        @data-change="handleDataChange"
      />
      <div v-else class="no-data">
        <p>点击"加载测试数据"按钮开始测试</p>
      </div>
    </div>
    
    <div class="test-info">
      <h3>测试说明：</h3>
      <ul>
        <li>表头应该固定在顶部，不随内容滚动</li>
        <li>当内容超出容器时，应该显示滚动条</li>
        <li>水平滚动时，表头应该同步滚动</li>
        <li>列宽可以通过拖拽调整</li>
        <li>双击单元格可以编辑内容</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import NativeTableComponent from './NativeTableComponent.vue'

const tableData = ref([])

// 加载测试数据
function loadTestData() {
  const headers = ['ID', '姓名', '部门', '职位', '邮箱', '电话', '入职日期', '薪资', '状态', '备注']
  const testRows = []
  
  for (let i = 1; i <= 20; i++) {
    testRows.push([
      i,
      `员工${i}`,
      `部门${Math.ceil(i / 5)}`,
      `职位${i % 3 + 1}`,
      `user${i}@company.com`,
      `138${String(i).padStart(8, '0')}`,
      `2024-01-${String(i % 28 + 1).padStart(2, '0')}`,
      `${5000 + i * 100}`,
      i % 2 === 0 ? '在职' : '离职',
      `这是员工${i}的备注信息，内容比较长用来测试表格的显示效果`
    ])
  }
  
  tableData.value = [headers, ...testRows]
}

// 添加更多数据
function addMoreData() {
  if (tableData.value.length === 0) {
    loadTestData()
    return
  }
  
  const currentLength = tableData.value.length - 1 // 减去表头
  const moreRows = []
  
  for (let i = 1; i <= 30; i++) {
    const id = currentLength + i
    moreRows.push([
      id,
      `新员工${id}`,
      `新部门${Math.ceil(id / 5)}`,
      `新职位${id % 4 + 1}`,
      `newuser${id}@company.com`,
      `139${String(id).padStart(8, '0')}`,
      `2024-02-${String(id % 28 + 1).padStart(2, '0')}`,
      `${6000 + id * 150}`,
      id % 3 === 0 ? '试用期' : '在职',
      `这是新员工${id}的详细备注信息，用于测试表格在大量数据下的滚动和显示性能`
    ])
  }
  
  tableData.value = [tableData.value[0], ...tableData.value.slice(1), ...moreRows]
}

// 清空数据
function clearData() {
  tableData.value = []
}

// 处理数据变化
function handleDataChange(newData) {
  tableData.value = newData
  console.log('表格数据已更新:', newData.length - 1, '条记录')
}
</script>

<style scoped>
.table-test-component {
  padding: 20px;
  background: #f8f9fb;
  min-height: 100vh;
}

h2 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.test-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  justify-content: center;
}

.test-btn {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.test-btn:hover {
  background: #66b1ff;
}

.table-wrapper {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.test-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #666;
}

.test-info {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.test-info h3 {
  color: #333;
  margin-bottom: 12px;
}

.test-info ul {
  color: #666;
  line-height: 1.6;
}

.test-info li {
  margin-bottom: 8px;
}
</style>
