import { FUniver } from '@univerjs/presets';
/**
 * 根据定位符列将快速取数sheet中的数据补全到收入成本测算sheet中
 * 处理列名映射关系，并从上一行补全公式
 * 优化版本：使用getFormulas()批量获取公式，使用setValues()批量回写
 * @param univerAPIInstance Univer API实例
 */
 
export async function fillRedTextToIncomeSheet(univerAPIInstance: FUniver): Promise<void> {
    try {
        // 获取快速取数sheet
        const quickDataSheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('快速取数');
        if (!quickDataSheet) {
            console.error('未找到快速取数sheet');
            return;
        }
        // 获取收入成本测算sheet
        const incomeSheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
        if (!incomeSheet) {
            console.error('未找到收入成本测算sheet');
            return;
        }
        let quickFormulas=incomeSheet.getRange(1,0,1,incomeSheet.getLastColumn()+1).getFormulas()
        const quickData=quickDataSheet.getRange(0,0,quickDataSheet.getLastRow()+1,quickDataSheet.getLastColumn()+1).getValues()
        const quickDataTitle=quickData[0]
        const quickLocatorIdx=quickDataTitle.indexOf('定位符')//快速取数sheet中定位符的列号
        const quickorgNameIdx=quickDataTitle.indexOf('利润中心组名称')//快速取数sheet中利润中心组名称的列号
        const quickProjectCodeIdx=quickDataTitle.indexOf('WBS元素')//快速取数sheet中项目编码的列号
        const quickProfitCenterIdx=quickDataTitle.indexOf('利润中心')//快速取数sheet中利润中心的列号
        const quickProjectNameIdx=quickDataTitle.indexOf('项目名称')//快速取数sheet中项目名称的列号
        
        const incomeData=incomeSheet.getRange(0,0,incomeSheet.getLastRow()+1,incomeSheet.getLastColumn()+1).getValues()
        const incomeTitle=incomeData[0]
        const incomeLocatorIdx=incomeTitle.indexOf('定位符')//收入成本测算sheet中定位符的列号
        const incomeorgNameIdx=incomeTitle.indexOf('组织机构')//收入成本测算sheet中利润中心组名称的列号
        const incomeProjectCodeIdx=incomeTitle.indexOf('项目编码')//收入成本测算sheet中项目编码的列号
        const incomeProfitCenterIdx=incomeTitle.indexOf('利润中心编码')//收入成本测算sheet中利润中心的列号
        const incomeProjectNameIdx=incomeTitle.indexOf('项目名称')//收入成本测算sheet中项目名称的列号
        
        //将快速取数里面的定位符放入字典,如果定位符不存在且不包含zz,则跳过
        const quickLocatorDict=new Map()
        for(let i=1;i<quickData.length;i++){
            if (quickData[i][quickLocatorIdx] && !quickData[i][quickLocatorIdx].includes('zz')) {
                quickLocatorDict.set(quickData[i][quickLocatorIdx],quickData[i])
            }
        }

        //遍历收入成本测算，将定位符也放入一个，如果快速取数里面没有，则将数据写入收入成本测算，实现补全
        const incomeLocatorDict=new Map()
        for(let i=1;i<incomeData.length;i++){
            if (incomeData[i][incomeLocatorIdx] && !incomeData[i][incomeLocatorIdx].includes('zz')) {
                incomeLocatorDict.set(incomeData[i][incomeLocatorIdx],incomeData[i])
            }
        }
        //遍历快速取数的字典，如果收入成本测算里面不存在则写入，实现补全
        //为加快速度，先建立一个同等宽度的二维数组
        const formula = univerAPIInstance?.getFormula()
        formula?.stopCalculation()
        //获取快速取数sheet的公式
        let k=incomeSheet.getLastRow()
        console.log(quickFormulas)
        let newarray=new Array()
        quickLocatorDict.forEach((row, locator) => {
            if (!incomeLocatorDict.has(locator)) {
                let row2=new Array(incomeSheet.getLastColumn()+1)
                row2[incomeLocatorIdx]=locator
                row2[incomeorgNameIdx]=row[quickorgNameIdx]
                row2[incomeProjectCodeIdx]=row[quickProjectCodeIdx]
                row2[incomeProfitCenterIdx]=row[quickProfitCenterIdx]
                row2[incomeProjectNameIdx]=row[quickProjectNameIdx]
                //将公式的引用行号修改为当前行，行号是A2,B2,$2这样的，用正则匹配后修改
                const currentRow = k+1;
                // 匹配单元格引用（可含工作表名），仅当行号未加$时替换为当前行
                // 例如：A2、$A2、A$2、$A$2、Sheet1!A2、'含空 格'!A2
                const refRegex = /((?:'[^']+'!|[A-Za-z0-9_]+!)?\$?[A-Za-z]{1,3})(\$?)(\d+)/g;
                // 遍历第一行的公式（quickFormulas[0]），因为只有一行公式
                for (let c = 0; c < quickFormulas[0].length; c++) {
                    const f = quickFormulas[0][c];
                    if (typeof f === 'string' && f.length > 0) {
                        row2[c] = f.replace(refRegex, (match, colPart, rowDollar, rowNum) => {
                            // 仅当行号前没有$时替换为当前行
                            return rowDollar ? match : `${colPart}${currentRow+1}`;
                        });
                    }
                }
                newarray.push(row2)
                k++
            }
        })
        console.log(newarray)
        let lastRow=incomeSheet.getLastRow()
        incomeSheet.insertRowsAfter(lastRow,newarray.length)
        incomeSheet.getRange(lastRow+1,0,newarray.length,newarray[0].length).setValues(newarray)
        //incomeSheet.getRange(incomeSheet.getLastRow(),0,newarray.length,newarray[0].length+1).setFormulas(newarray)
        formula?.executeCalculation()
    } catch (error) {
        console.error('函数执行出错:', error);
    }

        
}




