<template>
  <div class="sap-balance-export-container">
    <div class="export-form">
      <h2>SAP科目余额表导出</h2>
      <el-form :model="form" label-width="120px">
        <el-form-item label="存储位置">
          <el-select v-model="form.storageLocation" placeholder="请选择存储位置" style="width: 100%">
            <el-option label="本年科目余额表" value="current_year" />
            <el-option label="往年科目余额表" value="previous_year" />
          </el-select>
        </el-form-item>

        <el-form-item label="开始日期">
          <el-date-picker 
            v-model="form.startDate" 
            type="date" 
            placeholder="选择开始日期" 
            value-format="YYYY-MM-DD"
            format="YYYY年MM月DD日"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="结束日期">
          <el-date-picker 
            v-model="form.endDate" 
            type="date" 
            placeholder="选择结束日期" 
            value-format="YYYY-MM-DD"
            format="YYYY年MM月DD日"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="exportData" :loading="loading">
            {{ loading ? '导出中...' : '导出科目余额表' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const form = ref({
  storageLocation: 'current_year',
  startDate: '',
  endDate: ''
})

const loading = ref(false)
const dialogVisible = ref(false)

// 获取默认日期
const getDefaultDates = async () => {
  try {
    const response = await fetch('http://127.0.0.1:8000/api/get-default-dates', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      form.value.startDate = data.startDate || ''
      form.value.endDate = data.endDate || ''
    } else {
      console.warn('获取默认日期失败，使用空值')
    }
  } catch (error) {
    console.warn('获取默认日期时发生错误:', error.message)
  }
}

const exportData = async () => {
  if (!form.value.storageLocation) {
    ElMessage.warning('请选择存储位置')
    return
  }

  if (!form.value.startDate || !form.value.endDate) {
    ElMessage.warning('请选择开始日期和结束日期')
    return
  }

  if (form.value.startDate > form.value.endDate) {
    ElMessage.warning('开始日期不能晚于结束日期')
    return
  }

  loading.value = true

  try {
    // 发送POST请求导出科目余额表数据
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': 'sap科目余额表导出',
        '参数': [form.value.storageLocation, form.value.startDate, form.value.endDate]
      })
    })

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('科目余额表导出任务已启动')
    } else {
      ElMessage.error('科目余额表导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取默认日期
onMounted(() => {
  getDefaultDates()
})
</script>

<style scoped>
.sap-balance-export-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
  position: relative;
}

.export-form {
  max-width: 500px;
  margin: 0 auto;
}

.export-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}

.el-form-item {
  margin-bottom: 22px;
}

.el-select,
.el-date-picker {
  width: 100%;
}
</style>