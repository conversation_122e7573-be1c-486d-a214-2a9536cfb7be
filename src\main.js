import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'


// 引入 Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// 引入中文语言包
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import DataVVue3 from '@kjgl77/datav-vue3';
import VMdEditor from '@kangc/v-md-editor';
import '@kangc/v-md-editor/lib/style/base-editor.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';
import hljs from 'highlight.js';
// 引入 ECharts
import * as echarts from 'echarts'
import { provide } from 'vue'

// Register all Handsontable modules


const app = createApp(App)

// 全局注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
VMdEditor.use(githubTheme, {
  Hljs: hljs,
});
// 提供 echarts 实例
app.provide('ecTheme', 'light')
app.config.globalProperties.$echarts = echarts

app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})
app.use(DataVVue3)
app.use(VMdEditor);

app.mount('#app')
