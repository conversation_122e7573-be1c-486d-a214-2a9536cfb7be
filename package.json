{"name": "mytable", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.18", "@kjgl77/datav-vue3": "^1.7.4", "@univerjs/core": "^0.9.3", "@univerjs/design": "^0.9.3", "@univerjs/presets": "^0.9.3", "@univerjs/sheets": "^0.9.3", "@univerjs/sheets-table": "^0.9.3", "@univerjs/sheets-table-ui": "^0.9.3", "@univerjs/sheets-ui": "^0.9.3", "@univerjs/ui": "^0.9.3", "@visactor/vchart": "^1.13.10", "@visactor/vtable": "^1.18.4", "@visactor/vtable-editors": "^1.18.2", "@visactor/vtable-export": "^1.18.4", "@visactor/vue-vtable": "^1.18.2", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.11", "exceljs": "^4.4.0", "github-markdown-css": "^5.8.1", "marked": "^15.0.12", "react-dom": "^19.1.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "rollup-plugin-visualizer": "^6.0.0", "sass-embedded": "^1.89.0", "terser": "^5.42.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}