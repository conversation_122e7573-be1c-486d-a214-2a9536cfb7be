import { createUniver, defaultTheme, FUniver, LocaleType, merge, Univer } from '@univerjs/presets';

export default async function fetchDatadifference(univerAPIInstance:FUniver){
    try {
      const response = await fetch('http://localhost:8000/api/complete-table', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const formula = univerAPIInstance?.getFormula()
      formula?.stopCalculation()
      const data = await response.json() ;
      const sheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
      if (sheet) {
        const arr=sheet.getRange(0,0,sheet.getLastRow()+1,sheet.getLastColumn()+1).getValues();
        var localIndex=0;
        var differIncomeIndex=0;
        var differCostIndex=0;
        var stateIndex=0;//项目状态
        var closeTypeIndex=0;//结账模式
        var maxIncomeIndex=0;//商务虚线最大收入
        var contractIndex=0;//收入合同编号

        for(let i=0;i<arr[0].length;i++){
            if(arr[0][i]=='定位符'){localIndex=i}
            if(arr[0][i]=='缺失收入'){differIncomeIndex=i}
            if(arr[0][i]=='缺失成本'){differCostIndex=i}
            if(arr[0][i]=='项目状态'){stateIndex=i}
            if(arr[0][i]=='结账模式'){closeTypeIndex=i}
            if(arr[0][i]=='商务虚线最大收入'){maxIncomeIndex=i}
            if(arr[0][i]=='收入合同编号'){contractIndex=i}
        }
        for(let i=1;i<arr.length;i++){
            var localValue=arr[i][localIndex] as string;
            if(localValue in data){
              try{
                let subIncome=data[localValue]['收入差额']||0;
                let subCost=data[localValue]['成本差额']||0;
                sheet.getRange(i,differIncomeIndex).setValue(subIncome);
                sheet.getRange(i,differCostIndex).setValue(subCost);
                sheet.getRange(i,stateIndex).setValue(data[localValue]['状态']||'');
                sheet.getRange(i,closeTypeIndex).setValue("成本模式");
                sheet.getRange(i,maxIncomeIndex).setValue(data[localValue]['合同收入']||0);
                sheet.getRange(i,contractIndex).setValue(data[localValue]['合同编号'].replace(/'/g, "")||'');
              }catch(error){
                console.log(error);
              }
            }
                
      }}
      formula?.executeCalculation()
    } catch (error) {
      alert('获取数据失败: ' + error.message);
    }
  }



