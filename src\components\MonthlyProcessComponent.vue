<template>
  <div class="monthly-process-container">
    <!-- 内部确认 -->
    <div class="process-section">
      <h3 class="section-title">内部确认</h3>
      <div class="section-content">
        <div class="form-row">
          <label>年份：</label>
          <el-select v-model="internalConfirm.year" placeholder="2025" class="year-select">
            <el-option label="2025" value="2025" />
            <el-option label="2024" value="2024" />
            <el-option label="2023" value="2023" />
          </el-select>
          <label>月份：</label>
          <el-select v-model="internalConfirm.month" placeholder="7" class="month-select">
            <el-option v-for="month in 12" :key="month" :label="month" :value="month" />
          </el-select>
          <el-button type="success" @click="executeInternalConfirm" :loading="internalConfirm.loading"
            class="action-btn">
            <el-icon>
              <Right />
            </el-icon>
            内部确认
          </el-button>
        </div>
      </div>
    </div>

    <!-- 安全费计提 -->
    <div class="process-section">
      <h3 class="section-title">安全费计提</h3>
      <div class="section-content">
        <div class="form-row">
          <span class="info-text">计提安全生产费</span>
          <el-button type="success" @click="executeSafetyFee" :loading="safetyFee.loading" class="action-btn">
            <el-icon>
              <Right />
            </el-icon>
            安全费计提
          </el-button>
        </div>
      </div>
    </div>

    <!-- 暂估入账 -->
    <div class="process-section">
      <h3 class="section-title">暂估入账</h3>
      <div class="section-content">
        <div class="form-row">
          <span class="info-text">在数据展示器推送</span>
          <el-button type="success" @click="executeEstimateEntry" :loading="estimateEntry.loading" class="action-btn">
            <el-icon>
              <Right />
            </el-icon>
            自动生成暂估单据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 结账操作 -->
    <div class="process-section">
      <h3 class="section-title">结账操作</h3>
      <div class="section-content">
        <div class="button-group">
          <el-button type="success" @click="executeSapReconciliation" :loading="closeOperations.sapLoading"
            class="operation-btn">
            <el-icon>
              <Right />
            </el-icon>
            SAP收入测算
          </el-button>
          <el-button type="success" @click="executeSapConfirm" :loading="closeOperations.confirmLoading"
            class="operation-btn">
            <el-icon>
              <Right />
            </el-icon>
            SAP匡算确认
          </el-button>
          <el-button type="success" @click="executeMonthlyClose" :loading="closeOperations.closeLoading"
            class="operation-btn">
            <el-icon>
              <Right />
            </el-icon>
            生成中台匡算单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 结账制证 -->
    <div class="process-section">
      <h3 class="section-title">结账制证</h3>
      <div class="section-content">
        <div class="form-row">
          <span class="info-text">SAP最后制证</span>
          <el-button type="success" @click="executeVoucherGeneration" :loading="voucherGeneration.loading"
            class="action-btn">
            <el-icon>
              <Right />
            </el-icon>
            最终制证
          </el-button>
        </div>
      </div>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Right } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

// 响应式数据
const dialogVisible = ref(false)

const internalConfirm = ref({
  year: '2025',
  month: 7,
  loading: false
})

const safetyFee = ref({
  loading: false
})

const estimateEntry = ref({
  loading: false
})

const closeOperations = ref({
  sapLoading: false,
  confirmLoading: false,
  closeLoading: false
})

const voucherGeneration = ref({
  loading: false
})

const dataRepair = ref({
  year: '2025',
  month: 7,
  loading: false
})

// 各功能执行函数
const executeInternalConfirm = async () => {
  internalConfirm.value.loading = true
  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '内部确认',
        '参数': [internalConfirm.value.year, internalConfirm.value.month]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('内部确认任务已启动')
    } else {
      ElMessage.error('内部确认失败')
    }
  } catch (error) {
    ElMessage.error('内部确认过程中发生错误: ' + error.message)
  } finally {
    internalConfirm.value.loading = false
  }
}

const executeSafetyFee = async () => {
  safetyFee.value.loading = true
  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '安全费计提',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('安全费计提任务已启动')
    } else {
      ElMessage.error('安全费计提失败')
    }
  } catch (error) {
    ElMessage.error('安全费计提过程中发生错误: ' + error.message)
  } finally {
    safetyFee.value.loading = false
  }
}

const executeEstimateEntry = async () => {
  estimateEntry.value.loading = true
  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '自动生成暂估单据',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('暂估入账任务已启动')
    } else {
      ElMessage.error('暂估入账失败')
    }
  } catch (error) {
    ElMessage.error('暂估入账过程中发生错误: ' + error.message)
  } finally {
    estimateEntry.value.loading = false
  }
}

const executeSapReconciliation = async () => {
  closeOperations.value.sapLoading = true
  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': 'SAP收入测算',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('SAP收入测算任务已启动')
    } else {
      ElMessage.error('SAP收入测算失败')
    }
  } catch (error) {
    ElMessage.error('SAP收入测算过程中发生错误: ' + error.message)
  } finally {
    closeOperations.value.sapLoading = false
  }
}

const executeSapConfirm = async () => {
  closeOperations.value.confirmLoading = true
  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': 'SAP匡算确认',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('SAP匡算确认任务已启动')
    } else {
      ElMessage.error('SAP匡算确认失败')
    }
  } catch (error) {
    ElMessage.error('SAP匡算确认确认过程中发生错误: ' + error.message)
  } finally {
    closeOperations.value.confirmLoading = false
  }
}

const executeMonthlyClose = async () => {
  closeOperations.value.closeLoading = true
  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '生成中台匡算',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('生成中台任务已启动')
    } else {
      ElMessage.error('生成中台任务失败')
    }
  } catch (error) {
    ElMessage.error('生成中台任务过程中发生错误: ' + error.message)
  } finally {
    closeOperations.value.closeLoading = false
  }
}

const executeVoucherGeneration = async () => {
  voucherGeneration.value.loading = true
  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '最终制证',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('最终制证任务已启动')
    } else {
      ElMessage.error('最终制证失败')
    }
  } catch (error) {
    ElMessage.error('最终制证过程中发生错误: ' + error.message)
  } finally {
    voucherGeneration.value.loading = false
  }
}

const executeDataRepair = async () => {
  dataRepair.value.loading = true
  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '累计过账数据获取',
        '参数': [dataRepair.value.year, dataRepair.value.month]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('累计过账数据获取任务已启动')
    } else {
      ElMessage.error('累计过账数据获取失败')
    }
  } catch (error) {
    ElMessage.error('累计过账数据获取过程中发生错误: ' + error.message)
  } finally {
    dataRepair.value.loading = false
  }
}
</script>

<style scoped>
.monthly-process-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
}

.process-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.section-title {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-left: 4px solid #67c23a;
  padding-left: 10px;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.form-row label {
  color: #606266;
  font-weight: 500;
  min-width: 50px;
}

.year-select {
  width: 100px;
}

.month-select {
  width: 80px;
}

.info-text {
  color: #409eff;
  font-size: 14px;
  flex: 1;
}

.action-btn {
  min-width: 140px;
  height: 36px;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.operation-btn {
  min-width: 140px;
  height: 36px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monthly-process-container {
    padding: 15px;
  }

  .process-section {
    padding: 15px;
  }

  .form-row {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .button-group {
    flex-direction: column;
  }

  .action-btn,
  .operation-btn {
    width: 100%;
  }
}

/* 按钮悬停效果 */
.action-btn:hover,
.operation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>