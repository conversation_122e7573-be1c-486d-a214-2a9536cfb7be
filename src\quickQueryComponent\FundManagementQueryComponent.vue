<template>
  <BaseQueryComponent
    title="资金整理"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/fund-management'

// 查询字段配置
const queryFields = [
  {
    key: 'fiscalYear',
    label: '财年',
    type: 'select',
    placeholder: '请选择财年',
    width: '120px',
    options: [
      { label: '2024', value: '2024' },
      { label: '2023', value: '2023' },
      { label: '2022', value: '2022' },
      { label: '2021', value: '2021' }
    ]
  },
  {
    key: 'postingDate',
    label: '过帐日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'profitCenter',
    label: '利润中心',
    type: 'text',
    placeholder: '请输入利润中心',
    width: '150px'
  },
  {
    key: 'profitCenterDesc',
    label: '利润中心描述',
    type: 'text',
    placeholder: '请输入利润中心描述',
    width: '200px'
  },
  {
    key: 'voucherNumber',
    label: '凭证编号',
    type: 'text',
    placeholder: '请输入凭证编号',
    width: '180px'
  },
  {
    key: 'platformDocumentNumber2',
    label: '中台单据号2',
    type: 'text',
    placeholder: '请输入中台单据号2',
    width: '180px'
  },
  {
    key: 'generalLedgerAccount',
    label: '总账科目',
    type: 'text',
    placeholder: '请输入总账科目',
    width: '150px'
  },
  {
    key: 'supplierDesc',
    label: '供应商描述',
    type: 'text',
    placeholder: '请输入供应商描述',
    width: '200px'
  },
  {
    key: 'reason',
    label: '事由',
    type: 'text',
    placeholder: '请输入事由',
    width: '200px'
  },
  {
    key: 'internalBankAmount',
    label: '内行金额',
    type: 'amount-range'
  },
  {
    key: 'internalExternalFlow',
    label: '内外部流水',
    type: 'select',
    placeholder: '请选择内外部流水',
    width: '150px',
    options: [
      { label: '内部', value: '内部' },
      { label: '外部', value: '外部' }
    ]
  },
  {
    key: 'documentClassification',
    label: '单据分类',
    type: 'select',
    placeholder: '请选择单据分类',
    width: '150px',
    options: [
      { label: '收款', value: '收款' },
      { label: '付款', value: '付款' },
      { label: '转账', value: '转账' }
    ]
  },
  {
    key: 'internalBankCustomerMark',
    label: '内行客商标记',
    type: 'text',
    placeholder: '请输入内行客商标记',
    width: '150px'
  },
  {
    key: 'internalBankCustomerMark2',
    label: '内行客商标记2',
    type: 'text',
    placeholder: '请输入内行客商标记2',
    width: '150px'
  }
]
</script>
