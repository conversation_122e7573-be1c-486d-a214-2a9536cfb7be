<template>
  <div class="quick-fuzzy-match-container">
    <div class="match-form">
      <h2>快速模糊匹配</h2>
      
      <!-- 原始名称表格 -->
      <div class="table-section">
        <div class="section-header">
          <h3>原始名称</h3>
          <div class="action-buttons">
            <el-button type="primary" @click="addOriginalRow" size="small">
              <el-icon><Plus /></el-icon>
              添加行
            </el-button>
            <el-button @click="pasteOriginalData" size="small">
              <el-icon><Document /></el-icon>
              粘贴导入
            </el-button>
            <el-button @click="clearOriginalData" size="small" type="danger">
              <el-icon><Delete /></el-icon>
              清空数据
            </el-button>
          </div>
        </div>
        
        <!-- 原始名称表格 -->
        <el-table :data="originalData" border style="width: 100%; margin-top: 10px">
          <el-table-column prop="name" label="原始名称">
            <template #default="scope">
              <el-input v-model="scope.row.name" placeholder="请输入原始名称" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <el-button type="danger" size="small" @click="deleteOriginalRow(scope.$index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分隔线 -->
      <el-divider />
      
      <!-- 待匹配名称表格 -->
      <div class="table-section">
        <div class="section-header">
          <h3>待匹配名称</h3>
          <div class="action-buttons">
            <el-button type="primary" @click="addTargetRow" size="small">
              <el-icon><Plus /></el-icon>
              添加行
            </el-button>
            <el-button @click="pasteTargetData" size="small">
              <el-icon><Document /></el-icon>
              粘贴导入
            </el-button>
            <el-button @click="clearTargetData" size="small" type="danger">
              <el-icon><Delete /></el-icon>
              清空数据
            </el-button>
          </div>
        </div>
        
        <!-- 待匹配名称表格 -->
        <el-table :data="targetData" border style="width: 100%; margin-top: 10px">
          <el-table-column prop="name" label="待匹配名称">
            <template #default="scope">
              <el-input v-model="scope.row.name" placeholder="请输入待匹配名称" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <el-button type="danger" size="small" @click="deleteTargetRow(scope.$index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 操作按钮 -->
      <el-form label-width="120px" style="margin-top: 20px;">
        <el-form-item>
          <el-button type="primary" @click="quickMatch" :loading="quickMatchLoading">
            <el-icon>
              <Search />
            </el-icon>
            {{ quickMatchLoading ? '匹配中...' : '快速匹配' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Plus, Document, Delete } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const quickMatchLoading = ref(false)
const dialogVisible = ref(false)
const originalData = ref([{ name: '' }])
const targetData = ref([{ name: '' }])

// 原始名称表格操作
const addOriginalRow = () => {
  originalData.value.push({ name: '' })
}

const deleteOriginalRow = (index) => {
  if (originalData.value.length > 1) {
    originalData.value.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一行数据')
  }
}

const clearOriginalData = () => {
  if (confirm('确定要清空原始名称数据吗？')) {
    originalData.value = [{ name: '' }]
    ElMessage.success('原始名称数据已清空')
  }
}

const pasteOriginalData = async () => {
  try {
    const text = await navigator.clipboard.readText()
    if (!text) {
      ElMessage.warning('剪贴板为空')
      return
    }

    // 按行分割文本
    const lines = text.split('\n').filter(line => line.trim())
    const newData = []

    for (const line of lines) {
      // 按制表符、逗号或换行分割
      const parts = line.split(/\t|,/).map(part => part.trim())
      if (parts.length > 0 && parts[0]) {
        newData.push({ name: parts[0] })
      }
    }

    if (newData.length > 0) {
      originalData.value = newData
      ElMessage.success(`成功导入 ${newData.length} 行原始名称数据`)
    } else {
      ElMessage.warning('未找到有效数据')
    }
  } catch (error) {
    ElMessage.error('粘贴导入失败: ' + error.message)
  }
}

// 待匹配名称表格操作
const addTargetRow = () => {
  targetData.value.push({ name: '' })
}

const deleteTargetRow = (index) => {
  if (targetData.value.length > 1) {
    targetData.value.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一行数据')
  }
}

const clearTargetData = () => {
  if (confirm('确定要清空待匹配名称数据吗？')) {
    targetData.value = [{ name: '' }]
    ElMessage.success('待匹配名称数据已清空')
  }
}

const pasteTargetData = async () => {
  try {
    const text = await navigator.clipboard.readText()
    if (!text) {
      ElMessage.warning('剪贴板为空')
      return
    }

    // 按行分割文本
    const lines = text.split('\n').filter(line => line.trim())
    const newData = []

    for (const line of lines) {
      // 按制表符、逗号或换行分割
      const parts = line.split(/\t|,/).map(part => part.trim())
      if (parts.length > 0 && parts[0]) {
        newData.push({ name: parts[0] })
      }
    }

    if (newData.length > 0) {
      targetData.value = newData
      ElMessage.success(`成功导入 ${newData.length} 行待匹配名称数据`)
    } else {
      ElMessage.warning('未找到有效数据')
    }
  } catch (error) {
    ElMessage.error('粘贴导入失败: ' + error.message)
  }
}

// 快速匹配函数
const quickMatch = async () => {
  // 验证数据
  const validOriginalData = originalData.value.filter(row =>
    row.name && row.name.trim()
  )
  
  const validTargetData = targetData.value.filter(row =>
    row.name && row.name.trim()
  )

  if (validOriginalData.length === 0) {
    ElMessage.warning('请至少输入一条有效的原始名称')
    return
  }

  if (validTargetData.length === 0) {
    ElMessage.warning('请至少输入一条有效的待匹配名称')
    return
  }

  quickMatchLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '快速匹配',
        '参数': [{
          originalNames: validOriginalData.map(item => item.name),
          targetNames: validTargetData.map(item => item.name)
        }]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('快速匹配任务已启动')
    } else {
      ElMessage.error('快速匹配失败')
    }
  } catch (error) {
    ElMessage.error('匹配过程中发生错误: ' + error.message)
  } finally {
    quickMatchLoading.value = false
  }
}
</script>

<style scoped>
.quick-fuzzy-match-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
  position: relative;
}

.match-form {
  max-width: 800px;
  margin: 0 auto 40px auto;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.match-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.match-form:last-of-type {
  margin-bottom: 0;
}

.table-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .el-button {
  padding: 6px 12px;
}

/* 表格样式优化 */
.el-table {
  background-color: #fff;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  padding: 8px 0;
}

.el-input {
  width: 100%;
}

/* 分隔线样式 */
.el-divider {
  margin: 20px 0;
}
</style>