<template>
  <BaseQueryComponent
    title="成本表"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/cost-ledger'

// 查询字段配置
const queryFields = [
  {
    key: 'fiscalYear',
    label: '财年',
    type: 'select',
    placeholder: '请选择财年',
    width: '120px',
    options: [
      { label: '2024', value: '2024' },
      { label: '2023', value: '2023' },
      { label: '2022', value: '2022' },
      { label: '2021', value: '2021' }
    ]
  },
  {
    key: 'postingDate',
    label: '过帐日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'inputDate',
    label: '输入日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'voucherNumber',
    label: '凭证编号',
    type: 'text',
    placeholder: '请输入凭证编号',
    width: '180px'
  },
  {
    key: 'platformDocumentNumber',
    label: '中台单据号',
    type: 'text',
    placeholder: '请输入中台单据号',
    width: '180px'
  },
  {
    key: 'profitCenter',
    label: '利润中心',
    type: 'text',
    placeholder: '请输入利润中心',
    width: '150px'
  },
  {
    key: 'profitCenterDesc',
    label: '利润中心描述',
    type: 'text',
    placeholder: '请输入利润中心描述',
    width: '200px'
  },
  {
    key: 'costAccount',
    label: '成本科目',
    type: 'text',
    placeholder: '请输入成本科目',
    width: '150px'
  },
  {
    key: 'supplier',
    label: '供应商',
    type: 'text',
    placeholder: '请输入供应商编号',
    width: '150px'
  },
  {
    key: 'supplierDesc',
    label: '供应商描述',
    type: 'text',
    placeholder: '请输入供应商名称',
    width: '200px'
  },
  {
    key: 'text',
    label: '文本',
    type: 'text',
    placeholder: '请输入文本内容',
    width: '200px'
  },
  {
    key: 'accountingCost',
    label: '入账成本',
    type: 'amount-range'
  },
  {
    key: 'accountClassification',
    label: '科目分类',
    type: 'select',
    placeholder: '请选择科目分类',
    width: '150px',
    options: [
      { label: '直接成本', value: '直接成本' },
      { label: '间接成本', value: '间接成本' },
      { label: '管理费用', value: '管理费用' },
      { label: '财务费用', value: '财务费用' }
    ]
  },
  {
    key: 'costCategory',
    label: '成本类别',
    type: 'select',
    placeholder: '请选择成本类别',
    width: '150px',
    options: [
      { label: '人工费', value: '人工费' },
      { label: '材料费', value: '材料费' },
      { label: '机械费', value: '机械费' },
      { label: '其他费用', value: '其他费用' }
    ]
  }
]
</script>
