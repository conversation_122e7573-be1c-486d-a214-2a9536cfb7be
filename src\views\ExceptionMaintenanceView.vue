<template>
  <div class="exception-maintenance-container">
    <!-- 清单维护 -->
    <div class="maintenance-form">
      <h2>SAP异常清理</h2>
      <el-form label-width="120px">
        <el-form-item>
          <el-button type="primary" @click="maintainList" :loading="maintainListLoading">
            <el-icon>
              <Setting />
            </el-icon>
            {{ maintainListLoading ? '维护中...' : '清理SAP异常' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 导出数据重导入数据库 -->
    <div class="maintenance-form">
      <h2>数据库清空处理</h2>
      <el-form label-width="120px">
        <el-form-item>
          <div class="button-group">
            <el-button type="success" @click="reImportData" :loading="reImportDataLoading">
              <el-icon>
                <Upload />
              </el-icon>
              {{ reImportDataLoading ? '重导入中...' : '导出数据重导入数据库' }}
            </el-button>
            <el-button type="danger" @click="clearDatabase" :loading="clearDatabaseLoading">
              <el-icon>
                <Delete />
              </el-icon>
              {{ clearDatabaseLoading ? '清空中...' : '清空数据库' }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 异常数据处理 -->
    <div class="maintenance-form">
      <h2>异常数据处理</h2>
      <el-form label-width="120px">
        <el-form-item>
          <div class="button-group">
            <el-button type="warning" @click="exportExceptionData" :loading="exportExceptionDataLoading">
              <el-icon>
                <Download />
              </el-icon>
              {{ exportExceptionDataLoading ? '导出中...' : '异常数据导出' }}
            </el-button>
            <el-button type="primary" @click="importCorrectedData" :loading="importCorrectedDataLoading">
              <el-icon>
                <Upload />
              </el-icon>
              {{ importCorrectedDataLoading ? '导入中...' : '异常数据更正导入' }}
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- SAP明细下载失败重试 -->
    <div class="maintenance-form">
      <h2>SAP明细下载失败重试</h2>
      <el-form label-width="120px">
        <el-form-item>
          <el-tooltip content="请先关闭明细状态Excel文件" placement="top">
            <el-button type="info" @click="reExportDetailFailures" :loading="reExportDetailFailuresLoading">
              <el-icon>
                <Download />
              </el-icon>
              {{ reExportDetailFailuresLoading ? '重导出中...' : '明细账部分失败再次导出' }}
            </el-button>
          </el-tooltip>
        </el-form-item>
      </el-form>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Upload, Download, Delete } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const maintainListLoading = ref(false)
const reImportDataLoading = ref(false)
const clearDatabaseLoading = ref(false)
const exportExceptionDataLoading = ref(false)
const importCorrectedDataLoading = ref(false)
const reExportDetailFailuresLoading = ref(false)
const dialogVisible = ref(false)

// 清单维护
const maintainList = async () => {
  maintainListLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '清理SAP异常',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('清单维护任务已启动')
    } else {
      ElMessage.error('清单维护失败')
    }
  } catch (error) {
    ElMessage.error('维护过程中发生错误: ' + error.message)
  } finally {
    maintainListLoading.value = false
  }
}

// 导出数据重导入数据库
const reImportData = async () => {
  reImportDataLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '重新插入数据库',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('数据重导入任务已启动')
    } else {
      ElMessage.error('数据重导入失败')
    }
  } catch (error) {
    ElMessage.error('重导入过程中发生错误: ' + error.message)
  } finally {
    reImportDataLoading.value = false
  }
}

// 清空数据库
const clearDatabase = async () => {
  clearDatabaseLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '清空数据库',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('数据库清空任务已启动')
    } else {
      ElMessage.error('数据库清空失败')
    }
  } catch (error) {
    ElMessage.error('清空过程中发生错误: ' + error.message)
  } finally {
    clearDatabaseLoading.value = false
  }
}

// 异常数据导出
const exportExceptionData = async () => {
  exportExceptionDataLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '查询数据库',
        '参数': ["异常数据"]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('异常数据导出任务已启动')
    } else {
      ElMessage.error('异常数据导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    exportExceptionDataLoading.value = false
  }
}

// 异常数据更正导入
const importCorrectedData = async () => {
  importCorrectedDataLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '更新数据库',
        '参数': ["异常数据", true]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('异常数据更正导入任务已启动')
    } else {
      ElMessage.error('异常数据更正导入失败')
    }
  } catch (error) {
    ElMessage.error('导入过程中发生错误: ' + error.message)
  } finally {
    importCorrectedDataLoading.value = false
  }
}

// 明细账部分失败再次导出
const reExportDetailFailures = async () => {
  reExportDetailFailuresLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '明细账部分失败再次导出',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('明细账部分失败再次导出任务已启动')
    } else {
      ElMessage.error('明细账部分失败再次导出失败')
    }
  } catch (error) {
    ElMessage.error('导出过程中发生错误: ' + error.message)
  } finally {
    reExportDetailFailuresLoading.value = false
  }
}
</script>

<style scoped>
.exception-maintenance-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
  position: relative;
}

.maintenance-form {
  max-width: 600px;
  margin: 0 auto 40px auto;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.maintenance-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.maintenance-form:last-of-type {
  margin-bottom: 0;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.button-group .el-button {
  flex: 1;
  min-width: 140px;
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }

  .button-group .el-button {
    width: 100%;
  }
}
</style>