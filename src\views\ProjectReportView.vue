<template>
  <div class="project-report-dashboard">
    <!-- 顶部项目选择区域 -->
    <div class="project-selector-header">
      <div class="project-selector-container">
        <el-select
          v-model="selectedProjectId"
          placeholder="请选择项目"
          filterable
          :remote-method="searchProjects"
          :loading="projectLoading"
          @change="handleProjectChange"
          class="project-select"
          clearable
          @focus="handleSelectFocus"
        >
          <el-option
            v-for="project in projectList"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          >
            <span style="float: left">{{ project.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ project.code }}</span>
          </el-option>
        </el-select>
        <el-button type="primary" @click="refreshProjectData" :loading="dataLoading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button
          :type="compactLayout ? 'success' : 'default'"
          @click="toggleLayout"
          size="default"
        >
          {{ compactLayout ? '紧凑布局' : '标准布局' }}
        </el-button>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div :class="['main-content', { 'compact-layout': compactLayout }]" v-if="selectedProjectId">
      <!-- 左侧数据展示区 -->
      <div class="left-panel">
        <div class="data-section">
          <!-- 项目基本信息 -->
          <div class="section-header">
            <h3>项目基本信息</h3>
          </div>
          <div class="data-table">
            <div class="data-row">
              <div class="data-label">项目名称</div>
              <div class="data-value" @click="copyToClipboard(projectData.projectName, 'text')">{{ projectData.projectName || '-' }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">项目利润中心编码</div>
              <div class="data-value" @click="copyToClipboard(projectData.profitCenterCode, 'text')">{{ projectData.profitCenterCode || '-' }}</div>
            </div>
          </div>
        </div>

        <!-- 收入成本费用情况 -->
        <div class="data-section">
          <div class="section-header">
            <h3>收入成本费用情况</h3>
          </div>
          <div class="data-table">
            <div class="data-row">
              <div class="data-label">主营业务收入（财务口径）</div>
              <div class="data-value amount income" @click="copyToClipboard(projectData.mainBusinessIncome, 'amount')">{{ formatAmount(projectData.mainBusinessIncome) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">主营业务成本（财务口径）</div>
              <div class="data-value amount cost" @click="copyToClipboard(projectData.mainBusinessCost, 'amount')">{{ formatAmount(projectData.mainBusinessCost) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">附加税</div>
              <div class="data-value amount tax" @click="copyToClipboard(projectData.additionalTax, 'amount')">{{ formatAmount(projectData.additionalTax) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">累计确权（不含税）</div>
              <div class="data-value amount confirmation" @click="copyToClipboard(projectData.cumulativeConfirmation, 'amount')">{{ formatAmount(projectData.cumulativeConfirmation) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">项目累计间接费用（商务口径）</div>
              <div class="data-value amount cost" @click="copyToClipboard(projectData.cumulativeIndirectCost, 'amount')">{{ formatAmount(projectData.cumulativeIndirectCost) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">项目累计机械费用（商务口径）</div>
              <div class="data-value amount cost" @click="copyToClipboard(projectData.cumulativeMachineryCost, 'amount')">{{ formatAmount(projectData.cumulativeMachineryCost) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">非分包安全费（即财务一体化报销的安全支出）</div>
              <div class="data-value amount safety" @click="copyToClipboard(projectData.nonSubcontractSafetyFee, 'amount')">{{ formatAmount(projectData.nonSubcontractSafetyFee) }}</div>
            </div>
          </div>
        </div>

        <!-- 财务检查部分 -->
        <div class="data-section">
          <div class="section-header">
            <h3>财务检查部分</h3>
          </div>
          <div class="data-table">
            <div class="data-row">
              <div class="data-label">专项储备余额</div>
              <div class="data-value amount balance" @click="copyToClipboard(projectData.specialReserveBalance, 'amount')">{{ formatAmount(projectData.specialReserveBalance) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">合同履约成本余额</div>
              <div class="data-value amount balance" @click="copyToClipboard(projectData.contractPerformanceCostBalance, 'amount')">{{ formatAmount(projectData.contractPerformanceCostBalance) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">原材料余额</div>
              <div class="data-value amount balance" @click="copyToClipboard(projectData.rawMaterialBalance, 'amount')">{{ formatAmount(projectData.rawMaterialBalance) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">暂估应付余额</div>
              <div class="data-value amount balance" @click="copyToClipboard(projectData.estimatedPayableBalance, 'amount')">{{ formatAmount(projectData.estimatedPayableBalance) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">总包结算额</div>
              <div class="data-value amount confirmation" @click="copyToClipboard(projectData.totalContractSettlement, 'amount')">{{ formatAmount(projectData.totalContractSettlement) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">总包付款额</div>
              <div class="data-value amount cost" @click="copyToClipboard(projectData.totalContractPayment, 'amount')">{{ formatAmount(projectData.totalContractPayment) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">总包暂估额</div>
              <div class="data-value amount balance" @click="copyToClipboard(projectData.totalContractEstimate, 'amount')">{{ formatAmount(projectData.totalContractEstimate) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">往来对账检查</div>
              <div class="data-value amount" @click="copyToClipboard(projectData.reconciliationCheck, 'amount')">{{ formatAmount(projectData.reconciliationCheck) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">应交税费待转销</div>
              <div class="data-value amount tax" @click="copyToClipboard(projectData.taxPayablePendingWriteOff, 'amount')">{{ formatAmount(projectData.taxPayablePendingWriteOff) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">由税额反推开票额(只适用一般计税）</div>
              <div class="data-value amount tax" @click="copyToClipboard(projectData.invoiceAmountFromTax, 'amount')">{{ formatAmount(projectData.invoiceAmountFromTax) }}</div>
            </div>
          </div>
        </div>

        <!-- 资金收付情况 -->
        <div class="data-section">
          <div class="section-header">
            <h3>资金收付情况</h3>
          </div>
          <div class="data-table">
            <div class="data-row">
              <div class="data-label">累计含税确权</div>
              <div class="data-value amount confirmation" @click="copyToClipboard(projectData.cumulativeTaxInclusiveConfirmation, 'amount')">{{ formatAmount(projectData.cumulativeTaxInclusiveConfirmation) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">累计业主收款</div>
              <div class="data-value amount income" @click="copyToClipboard(projectData.cumulativeOwnerReceipt, 'amount')">{{ formatAmount(projectData.cumulativeOwnerReceipt) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">累计保证金收款</div>
              <div class="data-value amount income" @click="copyToClipboard(projectData.cumulativeDepositReceipt, 'amount')">{{ formatAmount(projectData.cumulativeDepositReceipt) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">累计分供结算</div>
              <div class="data-value amount confirmation" @click="copyToClipboard(projectData.cumulativeSubcontractorSettlement, 'amount')">{{ formatAmount(projectData.cumulativeSubcontractorSettlement) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">累计分供付款</div>
              <div class="data-value amount cost" @click="copyToClipboard(projectData.cumulativeSubcontractorPayment, 'amount')">{{ formatAmount(projectData.cumulativeSubcontractorPayment) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">累计税金付款</div>
              <div class="data-value amount tax" @click="copyToClipboard(projectData.cumulativeTaxPayment, 'amount')">{{ formatAmount(projectData.cumulativeTaxPayment) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">累计管理费用</div>
              <div class="data-value amount cost" @click="copyToClipboard(projectData.cumulativeManagementExpense, 'amount')">{{ formatAmount(projectData.cumulativeManagementExpense) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">累计其他类型收付款(反向推出）</div>
              <div class="data-value amount fund" @click="copyToClipboard(projectData.cumulativeOtherPayments, 'amount')">{{ formatAmount(projectData.cumulativeOtherPayments) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">原始存量</div>
              <div class="data-value amount fund" @click="copyToClipboard(projectData.originalStock, 'amount')">{{ formatAmount(projectData.originalStock) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">内部借款</div>
              <div class="data-value amount fund" @click="copyToClipboard(projectData.internalLoan, 'amount')">{{ formatAmount(projectData.internalLoan) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">保理借款</div>
              <div class="data-value amount fund" @click="copyToClipboard(projectData.factoringLoan, 'amount')">{{ formatAmount(projectData.factoringLoan) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">内部往来挂总部(一般为票据）</div>
              <div class="data-value amount fund" @click="copyToClipboard(projectData.internalTransactionHeadquarters, 'amount')">{{ formatAmount(projectData.internalTransactionHeadquarters) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">内部往来需调整</div>
              <div class="data-value amount fund" @click="copyToClipboard(projectData.internalTransactionAdjustment, 'amount')">{{ formatAmount(projectData.internalTransactionAdjustment) }}</div>
            </div>
            <div class="data-row">
              <div class="data-label">真实资金余额</div>
              <div class="data-value amount highlight" @click="copyToClipboard(projectData.realFundBalance, 'amount')">{{ formatAmount(projectData.realFundBalance) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧明细表格区 -->
      <div class="right-panel">
        <div class="detail-tabs-container">
          <div class="tabs-header">
            <h3>明细台账</h3>
            <div class="tab-buttons">
              <el-button
                v-for="tab in detailTabs"
                :key="tab.id"
                :type="activeDetailTab === tab.id ? 'primary' : 'default'"
                size="small"
                @click="switchDetailTab(tab.id)"
              >
                {{ tab.name }}
              </el-button>
              <el-button 
                type="success" 
                size="small" 
                icon="Refresh" 
                circle
                title="刷新当前表格"
                @click="refreshCurrentTable"
              ></el-button>
            </div>
          </div>

          <div class="table-wrapper">
            <NativeTableComponent
              v-if="currentTableData.length > 0"
              :data="currentTableData"
              :width="rightPanelWidth - 40"
              :height="500"
              :show-filter="true"
              :editable="false"
              :enable-copy-paste="true"
              :auto-width="true"
              :key="`table-${activeDetailTab}`"
              ref="vtable"
            />
            <div v-else class="no-data">
              <el-empty description="暂无数据" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 未选择项目时的提示 -->
    <div v-else class="no-project-selected">
      <el-empty description="请选择项目查看数据" />
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import NativeTableComponent from '@/components/NativeTableComponent.vue'
import axios from 'axios'

export default {
  name: 'ProjectReportView',
  components: {
    NativeTableComponent,
    Refresh
  },
  setup() {
    // 响应式数据
    const selectedProjectId = ref('')
    const projectList = ref([])
    const projectLoading = ref(false)
    const dataLoading = ref(false)
    const activeDetailTab = ref('payableSuppliers')
    const rightPanelWidth = ref(800)
    const compactLayout = ref(true) // 默认使用紧凑布局


    // 项目数据
    const projectData = reactive({
      projectName: '',
      profitCenterCode: '',
      mainBusinessIncome: 0,
      mainBusinessCost: 0,
      additionalTax: 0,
      cumulativeConfirmation: 0,
      cumulativeIndirectCost: 0,
      cumulativeMachineryCost: 0,
      nonSubcontractSafetyFee: 0,
      specialReserveBalance: 0,
      contractPerformanceCostBalance: 0,
      rawMaterialBalance: 0,
      estimatedPayableBalance: 0,
      totalContractSettlement: 0,
      totalContractPayment: 0,
      totalContractEstimate: 0,
      reconciliationCheck: 0,
      taxPayablePendingWriteOff: 0,
      invoiceAmountFromTax: 0,
      cumulativeTaxInclusiveConfirmation: 0,
      cumulativeOwnerReceipt: 0,
      cumulativeDepositReceipt: 0,
      cumulativeSubcontractorSettlement: 0,
      cumulativeSubcontractorPayment: 0,
      cumulativeTaxPayment: 0,
      cumulativeManagementExpense: 0,
      cumulativeOtherPayments: 0,
      originalStock: 0,
      internalLoan: 0,
      factoringLoan: 0,
      internalTransactionHeadquarters: 0,
      internalTransactionAdjustment: 0,
      realFundBalance: 0
    })

    // 开发环境配置
    const DEV_CONFIG = {
      useMockData: false, // 是否使用模拟数据
    }

    // 明细表格配置
    const detailTabs = ref([
      { id: 'payableSuppliers', name: '应付供应商汇总台账' },
      { id: 'payableContracts', name: '应付合同汇总台账' },
      { id: 'paymentLedger', name: '付款台账' },
      { id: 'subcontractorSettlement', name: '分供结算台账' },
      { id: 'costLedger', name: '成本台账' },
      { id: 'fundManagement', name: '资金整理' },
      { id: 'receiptLedger', name: '收款台账' },
      { id: 'externalConfirmation', name: '外部确权台账' },
      { id: 'safetyFeeLedger', name: '安全费台账' },
      { id: 'internalReconciliation', name: '内部对账' }
    ])

    // 明细表格数据
    const detailTablesData = reactive({
      payableSuppliers: [
        ['供应商名称', '供应商编码', '应付金额', '已付金额', '未付金额', '账期', '状态'],
        ['北京建材有限公司', 'SUP001', 1500000, 1200000, 300000, '30天', '正常'],
        ['上海钢铁集团', 'SUP002', 2800000, 2800000, 0, '45天', '已结清'],
        ['广州机械设备厂', 'SUP003', 950000, 500000, 450000, '60天', '逾期']
      ],
      payableContracts: [
        ['合同编号', '合同名称', '合同金额', '已结算金额', '未结算金额', '结算比例', '状态'],
        ['CT2024001', '主体工程施工合同', 5000000, 4500000, 500000, '90%', '执行中'],
        ['CT2024002', '装修工程合同', 1200000, 1200000, 0, '100%', '已完成'],
        ['CT2024003', '设备采购合同', 800000, 600000, 200000, '75%', '执行中']
      ],
      paymentLedger: [
        ['付款日期', '付款单号', '收款方', '付款金额', '付款方式', '用途', '审批人'],
        ['2024-01-15', 'PAY001', '北京建材有限公司', 500000, '银行转账', '材料款', '张经理'],
        ['2024-01-20', 'PAY002', '上海钢铁集团', 1000000, '银行转账', '钢材款', '李经理'],
        ['2024-02-01', 'PAY003', '员工工资', 300000, '银行转账', '人工费', '王经理']
      ],
      subcontractorSettlement: [
        ['分包商名称', '分包合同号', '结算期间', '结算金额', '质保金', '实付金额', '结算状态'],
        ['华建施工队', 'SUB001', '2024年1月', 800000, 40000, 760000, '已结算'],
        ['东方装修公司', 'SUB002', '2024年1月', 600000, 30000, 570000, '已结算'],
        ['精工安装队', 'SUB003', '2024年2月', 450000, 22500, 427500, '待结算']
      ],
      costLedger: [
        ['成本类别', '成本项目', '预算金额', '实际发生', '差异金额', '差异率', '负责部门'],
        ['人工成本', '施工人员工资', 1200000, 1150000, -50000, '-4.2%', '工程部'],
        ['材料成本', '主要建材', 2000000, 2100000, 100000, '5.0%', '采购部'],
        ['机械成本', '设备租赁费', 500000, 480000, -20000, '-4.0%', '设备部'],
        ['其他成本', '管理费用', 300000, 320000, 20000, '6.7%', '管理部']
      ],
      fundManagement: [
        ['资金类型', '期初余额', '本期收入', '本期支出', '期末余额', '资金来源', '备注'],
        ['自有资金', 2000000, 1500000, 1200000, 2300000, '项目回款', ''],
        ['银行贷款', 5000000, 0, 500000, 4500000, '建设银行', '年利率4.5%'],
        ['保证金', 500000, 100000, 0, 600000, '业主保证金', ''],
        ['其他资金', 200000, 50000, 80000, 170000, '临时周转', '']
      ],
      receiptLedger: [
        ['收款日期', '收款单号', '付款方', '收款金额', '收款方式', '项目阶段', '开票状态'],
        ['2024-01-10', 'REC001', '建设单位', 2000000, '银行转账', '首期款', '已开票'],
        ['2024-02-15', 'REC002', '建设单位', 1500000, '银行转账', '进度款', '已开票'],
        ['2024-03-20', 'REC003', '建设单位', 1000000, '银行转账', '进度款', '待开票']
      ],
      externalConfirmation: [
        ['确权日期', '确权单号', '确权金额', '确权类型', '对方单位', '确权依据', '状态'],
        ['2024-01-31', 'CON001', 1800000, '工程进度确权', '建设单位', '工程量清单', '已确认'],
        ['2024-02-28', 'CON002', 1200000, '材料确权', '建设单位', '材料验收单', '已确认'],
        ['2024-03-31', 'CON003', 900000, '工程进度确权', '建设单位', '工程量清单', '待确认']
      ],
      safetyFeeLedger: [
        ['支出日期', '支出项目', '支出金额', '支出类型', '受益人员', '审批状态', '备注'],
        ['2024-01-15', '安全帽采购', 15000, '劳保用品', '全体施工人员', '已审批', ''],
        ['2024-01-20', '安全培训费', 8000, '培训费用', '新入场人员', '已审批', ''],
        ['2024-02-01', '安全设施维护', 12000, '设施维护', '全体人员', '已审批', ''],
        ['2024-02-10', '安全检查费', 5000, '检查费用', '安全员', '已审批', '']
      ],
      internalReconciliation: [
        ['对账期间', '对账科目', '本方余额', '对方余额', '差异金额', '差异原因', '调整状态'],
        ['2024年1月', '应收账款', 2500000, 2500000, 0, '无差异', '已平衡'],
        ['2024年1月', '应付账款', 1800000, 1750000, 50000, '发票未到', '待调整'],
        ['2024年2月', '预付账款', 600000, 580000, 20000, '汇率差异', '待调整'],
        ['2024年2月', '其他应收款', 300000, 300000, 0, '无差异', '已平衡']
      ]
    })

    // 计算当前表格数据
    const currentTableData = computed(() => {
      const data = detailTablesData[activeDetailTab.value] || [];
      
      // 确保数据是二维数组格式
      if (data.length > 0) {
        try {
          // 如果第一行不是数组，说明整个数据可能是对象数组
          if (!Array.isArray(data[0]) && typeof data[0] === 'object') {
            // 获取所有对象的键作为标题行
            const headers = Object.keys(data[0]);
            // 将每个对象的值转为数组作为数据行
            const rows = data.map(item => Object.values(item));
            // 返回标准二维数组格式
            return [headers, ...rows];
          }
          
          // 如果已经是二维数组，直接返回
          if (Array.isArray(data) && Array.isArray(data[0])) {
            // 设置合理的列宽，确保表格可以完整显示
            const processedData = JSON.parse(JSON.stringify(data));
            
            // 设置每列最小宽度
            if (processedData.length > 0 && Array.isArray(processedData[0])) {
              console.log('计算表格列宽...');
            }
            
            return processedData;
          }
          
          // 其他情况返回空数组
          console.warn('无法识别的数据格式:', data);
          return [];
        } catch (error) {
          console.error('处理表格数据出错:', error);
          return [];
        }
      }
      
      return [];
    })

    // 格式化金额
    const formatAmount = (amount) => {
      if (amount === null || amount === undefined || amount === 0) {
        return '0.00'
      }
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2
      }).format(amount)
    }

    // 复制到剪贴板
    const copyToClipboard = (value, type) => {
      // 处理空值或无效值
      if (value === null || value === undefined || value === '') {
        ElMessage.warning('没有可复制的内容')
        return
      }

      // 根据类型处理要复制的值
      let textToCopy = ''
      if (type === 'amount') {
        // 对于金额类型，复制原始数值而非格式化后的值
        textToCopy = value.toString()
      } else {
        // 对于文本类型，直接复制
        textToCopy = value.toString()
      }

      // 使用现代的 Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(textToCopy)
          .then(() => {
            ElMessage.success('已复制到剪贴板')
          })
          .catch(err => {
            console.error('复制失败:', err)
            // 降级方案
            fallbackCopyTextToClipboard(textToCopy)
          })
      } else {
        // 降级方案
        fallbackCopyTextToClipboard(textToCopy)
      }
    }

    // 降级复制方案
    const fallbackCopyTextToClipboard = (text) => {
      const textArea = document.createElement('textarea')
      textArea.value = text
      
      // 避免滚动到页面底部
      textArea.style.top = '0'
      textArea.style.left = '0'
      textArea.style.position = 'fixed'
      
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          ElMessage.success('已复制到剪贴板')
        } else {
          ElMessage.error('复制失败，请手动复制')
        }
      } catch (err) {
        console.error('复制失败:', err)
        ElMessage.error('复制失败，请手动复制')
      }
      
      document.body.removeChild(textArea)
    }

    // 获取项目数据
    const fetchProjectData = async (projectId) => {
      // 如果开发环境配置为使用模拟数据，直接返回模拟数据
      if (DEV_CONFIG.useMockData) {
        const mockData = {
          projectName: '智慧城市建设项目',
          profitCenterCode: 'PC001',
          mainBusinessIncome: 5680000,
          mainBusinessCost: 3420000,
          additionalTax: 568000,
          cumulativeConfirmation: 4500000,
          cumulativeIndirectCost: 450000,
          cumulativeMachineryCost: 320000,
          nonSubcontractSafetyFee: 85000,
          specialReserveBalance: 120000,
          contractPerformanceCostBalance: 890000,
          rawMaterialBalance: 230000,
          estimatedPayableBalance: 150000,
          totalContractSettlement: 4200000,
          totalContractPayment: 3800000,
          totalContractEstimate: 400000,
          reconciliationCheck: 0,
          taxPayablePendingWriteOff: 45000,
          invoiceAmountFromTax: 0,
          cumulativeTaxInclusiveConfirmation: 5100000,
          cumulativeOwnerReceipt: 3408000,
          cumulativeDepositReceipt: 0,
          cumulativeSubcontractorSettlement: 2800000,
          cumulativeSubcontractorPayment: 2500000,
          cumulativeTaxPayment: 0,
          cumulativeManagementExpense: 0,
          cumulativeOtherPayments: 0,
          originalStock: 500000,
          internalLoan: 1000000,
          factoringLoan: 800000,
          internalTransactionHeadquarters: 200000,
          internalTransactionAdjustment: 50000,
          realFundBalance: 1250000
        }
        Object.assign(projectData, mockData)
        return
      }

      try {
        const apiUrl = 'http://localhost:8000/api/project/data'
        const response = await axios.post(apiUrl, {
          projectId: projectId,
          timestamp: new Date().toISOString()
        }, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000 // 10秒超时
        })

        if (response.status === 200 && response.data) {
          // 处理标准API响应格式: { code: 200, message: "success", data: {...} }
          const apiResponse = response.data;
          
          // 检查API响应状态码
          if (apiResponse.code === 200) {
            // 获取数据部分
            const responseData = apiResponse.data || {};
            
            // 提取项目基本信息
            const projectInfo = responseData.projectInfo || {};
            
            // 提取财务数据
            const financialData = responseData.financialData || {};
            
            // 提取明细表格数据
            const detailData = responseData.detailTables || {};
            
            // 处理明细表格数据 - 更新全局的detailTablesData
            Object.keys(detailData).forEach(tableKey => {
              if (Array.isArray(detailData[tableKey]) && detailData[tableKey].length > 0) {
                // 记录原始数据格式，用于调试
                console.log(`处理${tableKey}表格数据:`, {
                  数据类型: typeof detailData[tableKey],
                  第一行类型: typeof detailData[tableKey][0],
                  是否为二维数组: Array.isArray(detailData[tableKey][0]),
                  数据长度: detailData[tableKey].length,
                  原始数据: detailData[tableKey]
                });
                
                // 确保API返回的表格数据是二维数组格式（第一行为标题）
                if (Array.isArray(detailData[tableKey][0])) {
                  // 已经是二维数组格式，直接使用
                  console.log(`${tableKey}已是二维数组格式，直接使用`);
                  detailTablesData[tableKey] = JSON.parse(JSON.stringify(detailData[tableKey]));
                } else if (typeof detailData[tableKey][0] === 'object' && detailData[tableKey][0] !== null) {
                  // 对象数组格式，需要转换为二维数组
                  try {
                    const firstRow = detailData[tableKey][0];
                    const headers = Object.keys(firstRow);
                    const dataRows = detailData[tableKey].map(item => {
                      // 确保按照标题顺序提取值
                      return headers.map(key => item[key]);
                    });
                    // 组合成标准二维数组
                    const tableData = [headers, ...dataRows];
                    console.log(`${tableKey}转换为二维数组格式:`, {
                      标题行: headers,
                      数据行数: dataRows.length,
                      转换后数据: tableData
                    });
                    detailTablesData[tableKey] = tableData;
                  } catch (error) {
                    console.error(`转换${tableKey}数据出错:`, error);
                    // 出错时保留原始数据结构
                    detailTablesData[tableKey] = detailTablesData[tableKey] || [];
                  }
                } else {
                  // 数据格式不支持，使用空数组
                  console.error(`${tableKey}数据格式不支持:`, detailData[tableKey]);
                  detailTablesData[tableKey] = [];
                }
              } else {
                // 空数据或非数组
                console.warn(`${tableKey}无有效数据`);
                detailTablesData[tableKey] = [];
              }
            });
            
            // 合并项目信息和财务数据
            const processedData = {
              projectName: projectInfo.projectName || '未命名项目',
              profitCenterCode: projectInfo.profitCenterCode || '-',
              mainBusinessIncome: Number(financialData.mainBusinessIncome || 0),
              mainBusinessCost: Number(financialData.mainBusinessCost || 0),
              additionalTax: Number(financialData.additionalTax || 0),
              cumulativeConfirmation: Number(financialData.cumulativeConfirmation || 0),
              cumulativeIndirectCost: Number(financialData.cumulativeIndirectCost || 0),
              cumulativeMachineryCost: Number(financialData.cumulativeMachineryCost || 0),
              nonSubcontractSafetyFee: Number(financialData.nonSubcontractSafetyFee || 0),
              specialReserveBalance: Number(financialData.specialReserveBalance || 0),
              contractPerformanceCostBalance: Number(financialData.contractPerformanceCostBalance || 0),
              rawMaterialBalance: Number(financialData.rawMaterialBalance || 0),
              estimatedPayableBalance: Number(financialData.estimatedPayableBalance || 0),
              totalContractSettlement: Number(financialData.totalContractSettlement || 0),
              totalContractPayment: Number(financialData.totalContractPayment || 0),
              totalContractEstimate: Number(financialData.totalContractEstimate || 0),
              reconciliationCheck: Number(financialData.reconciliationCheck || 0),
              taxPayablePendingWriteOff: Number(financialData.taxPayablePendingWriteOff || 0),
              invoiceAmountFromTax: Number(financialData.invoiceAmountFromTax || 0),
              cumulativeTaxInclusiveConfirmation: Number(financialData.cumulativeTaxInclusiveConfirmation || 0),
              cumulativeOwnerReceipt: Number(financialData.cumulativeOwnerReceipt || 0),
              cumulativeDepositReceipt: Number(financialData.cumulativeDepositReceipt || 0),
              cumulativeSubcontractorSettlement: Number(financialData.cumulativeSubcontractorSettlement || 0),
              cumulativeSubcontractorPayment: Number(financialData.cumulativeSubcontractorPayment || 0),
              cumulativeTaxPayment: Number(financialData.cumulativeTaxPayment || 0),
              cumulativeManagementExpense: Number(financialData.cumulativeManagementExpense || 0),
              cumulativeOtherPayments: Number(financialData.cumulativeOtherPayments || 0),
              originalStock: Number(financialData.originalStock || 0),
              internalLoan: Number(financialData.internalLoan || 0),
              factoringLoan: Number(financialData.factoringLoan || 0),
              internalTransactionHeadquarters: Number(financialData.internalTransactionHeadquarters || 0),
              internalTransactionAdjustment: Number(financialData.internalTransactionAdjustment || 0),
              realFundBalance: Number(financialData.realFundBalance || 0)
            };
            
            // 更新项目数据
            Object.assign(projectData, processedData)
            console.log('项目数据加载成功:', processedData)
          } else {
            // API返回了非成功状态码
            throw new Error(`API错误: ${apiResponse.message || '未知错误'}`)
          }
        } else {
          throw new Error('数据返回格式异常')
        }
      } catch (error) {
        console.error('获取项目数据失败:', error)
        let errorMessage = '获取项目数据失败'
        if (error.response) {
          // 服务器返回错误状态码
          errorMessage += `，服务器返回：${error.response.status}`
        } else if (error.request) {
          // 请求发出但没有收到响应
          errorMessage += '，无法连接到服务器'
        } else {
          // 请求配置出错
          errorMessage += `，${error.message}`
        }
        ElMessage.error(errorMessage)
        
        // 当获取数据失败时，使用模拟数据以保证界面正常显示
        const mockData = {
          projectName: '智慧城市建设项目（模拟数据）',
          profitCenterCode: 'PC001',
          mainBusinessIncome: 5680000,
          cumulativeConfirmation: 4500000,
          cumulativeIndirectCost: 450000,
          cumulativeMachineryCost: 320000,
          nonSubcontractSafetyFee: 85000,
          specialReserveBalance: 120000,
          contractPerformanceCostBalance: 890000,
          rawMaterialBalance: 230000,
          estimatedPayableBalance: 150000,
          totalContractSettlement: 4200000,
          totalContractPayment: 3800000,
          totalContractEstimate: 400000,
          reconciliationCheck: 0,
          taxPayablePendingWriteOff: 45000,
          invoiceAmountFromTax: 0,
          cumulativeTaxInclusiveConfirmation: 5100000,
          cumulativeOwnerReceipt: 3408000,
          cumulativeDepositReceipt: 0,
          cumulativeSubcontractorSettlement: 2800000,
          cumulativeSubcontractorPayment: 2500000,
          cumulativeTaxPayment: 0,
          cumulativeManagementExpense: 0,
          cumulativeOtherPayments: 0,
          originalStock: 500000,
          internalLoan: 1000000,
          factoringLoan: 800000,
          internalTransactionHeadquarters: 200000,
          internalTransactionAdjustment: 50000,
          realFundBalance: 1250000
        }
        Object.assign(projectData, mockData)
        console.warn('使用模拟数据作为后备')
      }
    }

    // 搜索项目
    const searchProjects = async (query) => {
      projectLoading.value = true;
      try {
        // 清除多余空格
        const trimmedQuery = (query || '').trim();
        console.log('搜索项目，关键词:', trimmedQuery);

        const apiUrl = 'http://localhost:8000/api/project/search'
        const response = await axios.post(apiUrl, {
          query: trimmedQuery,
          timestamp: new Date().toISOString()
        }, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 5000 // 5秒超时
        })

        if (response.status === 200 && response.data) {
          console.log('API响应:', response.data);

          // 尝试处理各种可能的API响应格式
          let projects = [];
          const apiResponse = response.data;

          // 情况1: { code: 200, message: "success", data: [...] }
          if (apiResponse.code === 200 && apiResponse.data) {
            projects = Array.isArray(apiResponse.data) ? apiResponse.data :
                      (apiResponse.data.projects || apiResponse.data.list || []);
          }
          // 情况2: 直接返回数组
          else if (Array.isArray(apiResponse)) {
            projects = apiResponse;
          }
          // 情况3: { data: [...] } 或 { result: [...] } 或 { list: [...] }
          else if (apiResponse.data || apiResponse.result || apiResponse.list) {
            projects = apiResponse.data || apiResponse.result || apiResponse.list || [];
            projects = Array.isArray(projects) ? projects : [];
          }

          // 确保每个项目对象有必要的字段并进行映射标准化
          if (projects.length > 0) {
            projectList.value = projects.map(p => ({
              id: p.id || p.projectId || p.project_id || '',
              name: p.name || p.projectName || p.project_name || '',
              code: p.code || p.projectCode || p.project_code || ''
            }));
            console.log('API返回项目列表:', projectList.value);
          } else {
            console.warn('API返回了空的项目列表，将使用本地数据');
            useLocalProjectData(trimmedQuery);
          }
        } else {
          console.warn('API响应异常，将使用本地数据');
          useLocalProjectData(trimmedQuery);
        }
      } catch (error) {
        console.error('搜索项目失败:', error);
        let errorMessage = '搜索项目失败';
        if (error.response) {
          errorMessage += `，服务器返回：${error.response.status}`;
        } else if (error.request) {
          errorMessage += '，无法连接到服务器';
        } else {
          errorMessage += `，${error.message}`;
        }
        console.warn(errorMessage + '，使用本地数据');

        useLocalProjectData(trimmedQuery);
      } finally {
        projectLoading.value = false;
      }
    };

    // 使用本地数据进行项目搜索（作为后备）
    const useLocalProjectData = (query) => {
      // 模拟项目数据
      const mockProjects = [
        { id: 'PRJ001', name: '智慧城市建设项目', code: 'PRJ2024001' },
        { id: 'PRJ002', name: '企业数字化转型项目', code: 'PRJ2024002' },
        { id: 'PRJ003', name: '工业互联网平台建设', code: 'PRJ2024003' },
        { id: 'PRJ004', name: '绿色能源开发项目', code: 'PRJ2024004' },
        { id: 'PRJ005', name: '智能制造升级项目', code: 'PRJ2024005' },
        { id: 'PRJ006', name: '南方广东省人民医院改造工程', code: 'L306200241' },
        { id: 'PRJ007', name: '数据中心能效优化项目', code: 'PRJ2024007' },
        { id: 'PRJ008', name: '智能楼宇管理系统', code: 'PRJ2024008' },
        { id: 'PRJ009', name: '可再生能源集成工程', code: 'PRJ2024009' },
        { id: 'PRJ010', name: '工业园区基础设施升级', code: 'PRJ2024010' }
      ];
      
      // 如果有搜索词则进行模糊匹配
      if (query) {
        const lowercaseQuery = query.toLowerCase();
        console.log('执行本地搜索，关键词:', lowercaseQuery);
        
        // 增强的模糊搜索：支持拼音首字母、部分匹配
        const filteredProjects = mockProjects.filter(project => {
          const projectName = project.name.toLowerCase();
          const projectCode = project.code.toLowerCase();
          
          // 检查名称和编码是否包含查询字符串（部分匹配）
          return projectName.includes(lowercaseQuery) || 
                 projectCode.includes(lowercaseQuery) ||
                 // 获取名称的每个字的首字母（模拟拼音首字母）
                 projectName.split('').some(char => char.toLowerCase() === lowercaseQuery);
        });
        console.log('本地搜索结果:', filteredProjects);
        projectList.value = filteredProjects;
      } else {
        // 无搜索词时返回全部
        projectList.value = [...mockProjects];
      }
      
      console.log('使用本地数据，匹配到项目数:', projectList.value.length);
    };

    // 处理项目切换
    const handleProjectChange = async (projectId) => {
      if (!projectId) return
      
      dataLoading.value = true
      try {
        // 调用API获取项目数据
        await fetchProjectData(projectId)
        
        // 由于表格数据可能变化，强制重新渲染
        await nextTick()
        
        // 尝试重新激活当前标签页
        if (activeDetailTab.value) {
          const currentTab = activeDetailTab.value
          activeDetailTab.value = ''
          await nextTick()
          activeDetailTab.value = currentTab
        }
        
        ElMessage.success('项目数据加载成功')
      } catch (error) {
        console.error('获取项目数据失败:', error)
        ElMessage.error('获取项目数据失败')
      } finally {
        dataLoading.value = false
      }
    }

    // 刷新项目数据
    const refreshProjectData = async () => {
      if (!selectedProjectId.value) {
        ElMessage.warning('请先选择项目')
        return
      }
      await handleProjectChange(selectedProjectId.value)
    }

    // 切换明细表格
    const switchDetailTab = (tabId) => {
      activeDetailTab.value = tabId
      // 添加日志，查看当前表格数据结构
      console.log(`切换到${tabId}表格，数据结构:`, {
        原始数据: detailTablesData[tabId],
        计算后数据: currentTableData.value,
        是否为二维数组: Array.isArray(detailTablesData[tabId]) && detailTablesData[tabId].length > 0 && Array.isArray(detailTablesData[tabId][0]),
        第一行元素类型: detailTablesData[tabId]?.length > 0 ? typeof detailTablesData[tabId][0] : '无数据'
      })
    }
    
    // 刷新当前表格
    const refreshCurrentTable = async () => {
      if (!activeDetailTab.value) {
        ElMessage.warning('请先选择明细表格')
        return
      }
      
      try {
        // 获取当前标签
        const currentTab = activeDetailTab.value
        // 强制重新渲染表格
        activeDetailTab.value = ''
        await nextTick()
        activeDetailTab.value = currentTab
        
        ElMessage.success('表格已刷新')
      } catch (error) {
        console.error('刷新表格失败:', error)
        ElMessage.error('刷新表格失败')
      }
    }

    // 切换布局模式
    const toggleLayout = () => {
      compactLayout.value = !compactLayout.value
      // 切换布局后重新计算右侧面板宽度
      setTimeout(() => {
        const windowWidth = window.innerWidth
        const leftPanelSpace = compactLayout.value ? 300 : 350
        rightPanelWidth.value = Math.max(600, windowWidth - leftPanelSpace)
      }, 100)
    }

    // 处理选择器获得焦点
    const handleSelectFocus = () => {
      console.log('项目选择器获得焦点，当前项目列表长度:', projectList.value.length)
      // 如果项目列表为空，尝试加载
      if (projectList.value.length === 0) {
        console.log('项目列表为空，尝试加载...')
        searchProjects('')
      }
    }

    // 初始化项目列表
    const initializeProjectList = async () => {
      try {
        // 首先尝试加载所有项目（空查询）
        await searchProjects('')
        console.log('初始化项目列表成功，项目数量:', projectList.value.length)

        // 如果有项目且没有选中项目，自动选择第一个
        if (projectList.value.length > 0 && !selectedProjectId.value) {
          selectedProjectId.value = projectList.value[0].id
          await handleProjectChange(selectedProjectId.value)
        }
      } catch (error) {
        console.error('初始化项目列表失败:', error)
        // 确保使用本地数据作为后备
        useLocalProjectData('')
      }
    }

    // 初始化
    onMounted(() => {
      // 计算右侧面板宽度
      const updateRightPanelWidth = () => {
        const windowWidth = window.innerWidth
        // 根据布局模式计算左侧面板占用空间
        const leftPanelSpace = compactLayout.value ? 300 : 350 // 紧凑模式280px + gap + padding
        rightPanelWidth.value = Math.max(600, windowWidth - leftPanelSpace)
      }

      updateRightPanelWidth()
      window.addEventListener('resize', updateRightPanelWidth)

      // 初始化项目列表
      initializeProjectList()
    })

    return {
      selectedProjectId,
      projectList,
      projectLoading,
      dataLoading,
      activeDetailTab,
      rightPanelWidth,
      compactLayout,

      projectData,
      detailTabs,
      detailTablesData,
      currentTableData,
      formatAmount,
      copyToClipboard,
      searchProjects,
      handleProjectChange,
      handleSelectFocus,
      refreshProjectData,
      switchDetailTab,
      refreshCurrentTable,
      toggleLayout
    }
  }
};
</script>

<style scoped>
.project-report-dashboard {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f4fd 100%);
  min-height: 100vh;
  width: 100%;
  height: auto;
  position: relative;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

/* 顶部项目选择区域 */
.project-selector-header {
  background: linear-gradient(135deg, #fff 0%, #f8fbff 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.08);
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(64, 158, 255, 0.1);
  width: 100%;
}

.project-selector-container {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap; /* 允许在小屏幕上换行 */
}

.project-select {
  min-width: 300px;
  flex: 1;
  max-width: 500px;
}

/* 主体内容区域 */
.main-content {
  display: flex;
  flex-direction: row;
  gap: 8px;
  min-height: calc(100vh - 200px);
  align-items: stretch;
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  flex: 1;
  overflow: visible;
}

/* 左侧数据展示区 */
.left-panel {
  width: 320px;
  max-width: 320px;
  min-width: 320px;
  background: linear-gradient(135deg, #fff 0%, #fafbff 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.08);
  overflow-y: auto;
  flex-shrink: 0;
  border: 1px solid rgba(64, 158, 255, 0.1);
  max-height: calc(100vh - 200px);
  padding: 12px;
  margin: 0;
}

.data-section {
  margin-bottom: 12px;
}

.data-section:last-child {
  margin-bottom: 0;
}

.section-header {
  margin-bottom: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #409EFF 0%, #66b3ff 100%);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.section-header h3 {
  margin: 0;
  font-size: 13px;
  color: #fff;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.data-table {
  border: 1px solid #e1e8f0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.data-row {
  display: flex;
  border-bottom: 1px solid #e1e8f0;
  transition: background-color 0.2s ease;
}

.data-row:last-child {
  border-bottom: none;
}

.data-row:hover {
  background-color: rgba(64, 158, 255, 0.02);
}

.data-label {
  width: 160px;
  flex-shrink: 0;
  padding: 6px 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8f4fd 100%);
  border-right: 1px solid #e1e8f0;
  font-size: 11px;
  color: #4a5568;
  font-weight: 500;
  line-height: 1.2;
}

.data-value {
  flex: 1;
  padding: 6px 8px;
  font-size: 11px;
  color: #2d3748;
  background-color: #fff;
  transition: all 0.2s ease;
  line-height: 1.2;
  cursor: pointer;
  position: relative;
}

.data-value:hover {
  background-color: rgba(64, 158, 255, 0.05);
}

.data-value::after {
  content: '点击复制';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 9px;
  color: #909399;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.data-value:hover::after {
  opacity: 0.7;
}

.data-value.amount {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  text-align: right;
  color: #2b6cb0;
}

.data-value.highlight {
  background: linear-gradient(135deg, #ecf5ff 0%, #dbeafe 100%);
  color: #1e40af;
  font-weight: 700;
  border-left: 3px solid #409EFF;
}

/* 不同类型数据的颜色标识 */
.data-value.income {
  color: #059669;
  border-left: 3px solid #10b981;
}

.data-value.cost {
  color: #dc2626;
  border-left: 3px solid #ef4444;
}

.data-value.tax {
  color: #7c3aed;
  border-left: 3px solid #8b5cf6;
}

.data-value.confirmation {
  color: #0891b2;
  border-left: 3px solid #06b6d4;
}

.data-value.safety {
  color: #ea580c;
  border-left: 3px solid #f97316;
}

.data-value.balance {
  color: #1d4ed8;
  border-left: 3px solid #3b82f6;
}

.data-value.fund {
  color: #7c2d12;
  border-left: 3px solid #a16207;
}

/* 右侧明细表格区 */
.right-panel {
  flex: 1;
  background: linear-gradient(135deg, #fff 0%, #fafbff 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.08);
  padding: 16px;
  border: 1px solid rgba(64, 158, 255, 0.1);
  min-width: 600px;
  height: calc(100vh - 200px);
  overflow: auto;
  margin: 0;
  display: flex;
  flex-direction: column;
  position: relative;
}

.detail-tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: visible;
  width: 100%;
}

.table-wrapper {
  flex: 1;
  display: flex;
  overflow: visible;
  position: relative;
  height: calc(100% - 80px);
  width: 100%;
  min-width: 0;
}

.tabs-header {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.tabs-header h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #2d3748;
  font-weight: 600;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f7fafc 0%, #e2e8f0 100%);
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.tab-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 未选择项目时的提示 */
.no-project-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 1300px) {
  .main-content {
    flex-direction: column;
    min-height: auto;
    height: auto;
  }

  .left-panel {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    max-height: 400px;
  }

  .right-panel {
    min-width: auto;
    width: 100%;
    height: auto;
    min-height: 600px;
  }
}

@media (max-width: 768px) {
  .project-report-dashboard {
    padding: 10px;
  }

  .project-selector-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .project-select {
    min-width: 100%;
    max-width: 100%;
  }
  
  .main-content {
    gap: 16px;
  }

  .left-panel, .right-panel {
    padding: 16px;
  }

  .data-row {
    flex-direction: column;
  }

  .data-label {
    border-right: none;
    border-bottom: 1px solid #ebeef5;
    min-width: auto;
  }

  .data-value {
    min-width: auto;
  }

  .table-wrapper {
    height: auto;
    min-height: 500px;
  }
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar {
  width: 8px;
}

.left-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.left-panel::-webkit-scrollbar-thumb {
  background: #409EFF;
  border-radius: 4px;
}

.left-panel::-webkit-scrollbar-thumb:hover {
  background: #337ecc;
}

/* 确保滚动条始终可见 */
.left-panel {
  scrollbar-width: thin;
  scrollbar-color: #409EFF #f1f1f1;
}

/* 紧凑布局样式 */
.main-content.compact-layout {
  gap: 4px;
}

.main-content.compact-layout .left-panel {
  width: 280px;
  max-width: 280px;
  min-width: 280px;
  padding: 8px;
}

.main-content.compact-layout .right-panel {
  padding: 12px;
}

.main-content.compact-layout .data-section {
  margin-bottom: 8px;
}

.main-content.compact-layout .section-header {
  padding: 6px 10px;
  margin-bottom: 6px;
}

.main-content.compact-layout .section-header h3 {
  font-size: 12px;
}

.main-content.compact-layout .data-label {
  width: 140px;
  padding: 4px 6px;
  font-size: 10px;
}

.main-content.compact-layout .data-value {
  padding: 4px 6px;
  font-size: 10px;
}

/* 强制大屏幕左右布局 */
@media (min-width: 1301px) {
  .main-content {
    flex-direction: row;
    gap: 8px;
  }

  .left-panel {
    width: 320px;
    max-width: 320px;
    min-width: 320px;
    max-height: calc(100vh - 200px);
  }

  .right-panel {
    min-width: 600px;
    height: calc(100vh - 200px);
  }

  .main-content.compact-layout .left-panel {
    width: 280px;
    max-width: 280px;
    min-width: 280px;
  }
}

/* VTable组件样式 */
.table-wrapper :deep(.vtable-component) {
  width: 100%;
  max-width: 100%;
  height: 100%;
  flex: 1;
}

.table-wrapper :deep(.table-container) {
  width: 100% !important;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.table-wrapper :deep(.filter-panel) {
  margin-bottom: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

</style>
