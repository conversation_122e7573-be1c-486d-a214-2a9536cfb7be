/**
 * 通用表格组件类型定义
 */

// 表格数据类型 - 表名到二维数组的映射
export type TableData = Record<string, any[][]>

// 单元格值类型
export type CellValue = string | number | boolean | null | undefined | CellObject

// Univer 单元格对象格式（用于处理字符型数字）
export interface CellObject {
  v: string | number | boolean  // 值
  t: number                     // 类型：1=字符串, 2=数字, 3=布尔值
  s: any                        // 样式
}

// 数据提供函数类型
export type DataProvider = () => Promise<TableData>

// 组件 Props 接口
export interface UniversalTableProps {
  /** 数据获取函数 - 父组件传入，返回 Promise<Record<string, any[][]>> */
  dataProvider?: DataProvider
  /** 初始数据 - 可选，格式为 { 表名: 二维数组 } */
  initialData?: TableData
  /** 工作簿名称 */
  workbookName?: string
}

// 组件 Emits 接口
export interface UniversalTableEmits {
  /** 数据变化时触发，返回所有表格数据 */
  dataChange: (data: TableData) => void
  /** 表格初始化完成 */
  initialized: () => void
  /** 错误事件 */
  error: (error: string) => void
}

// 组件暴露的方法接口
export interface UniversalTableExposed {
  /** 加载数据到表格 */
  loadData: (data: TableData) => Promise<void>
  /** 获取当前所有表格数据 */
  getAllTableData: () => TableData
  /** 刷新数据（调用 dataProvider） */
  refreshData: () => Promise<void>
  /** 导出数据（触发 dataChange 事件） */
  exportData: () => void
}

// 字符型数字处理配置
export interface TextColumnConfig {
  /** 需要处理为文本的列名关键词 */
  keywords: string[]
  /** 是否处理纯数字列名 */
  processNumericHeaders: boolean
}

// 默认的文本列配置
export const DEFAULT_TEXT_COLUMN_CONFIG: TextColumnConfig = {
  keywords: ['身份证号', '证件号码', '银行卡号', '工号'],
  processNumericHeaders: true
}

// 表格样式配置
export interface TableStyleConfig {
  /** 表头背景色 */
  headerBackgroundColor: string
  /** 表头字体粗细 */
  headerFontWeight: string
  /** 默认行数 */
  defaultRows: number
  /** 默认列数 */
  defaultCols: number
  /** 数据行额外行数 */
  extraRows: number
  /** 数据列额外列数 */
  extraCols: number
}

// 默认样式配置
export const DEFAULT_STYLE_CONFIG: TableStyleConfig = {
  headerBackgroundColor: '#f0f0f0',
  headerFontWeight: 'bold',
  defaultRows: 100,
  defaultCols: 50,
  extraRows: 10,
  extraCols: 5
}

// 错误类型枚举
export enum UniversalTableError {
  INIT_FAILED = 'INIT_FAILED',
  LOAD_DATA_FAILED = 'LOAD_DATA_FAILED',
  DATA_PROVIDER_FAILED = 'DATA_PROVIDER_FAILED',
  INVALID_DATA_FORMAT = 'INVALID_DATA_FORMAT',
  UNIVER_NOT_INITIALIZED = 'UNIVER_NOT_INITIALIZED'
}

// 错误信息映射
export const ERROR_MESSAGES: Record<UniversalTableError, string> = {
  [UniversalTableError.INIT_FAILED]: 'Univer 初始化失败',
  [UniversalTableError.LOAD_DATA_FAILED]: '数据加载失败',
  [UniversalTableError.DATA_PROVIDER_FAILED]: '数据获取失败',
  [UniversalTableError.INVALID_DATA_FORMAT]: '数据格式无效',
  [UniversalTableError.UNIVER_NOT_INITIALIZED]: 'Univer 未初始化'
}

// 工具函数类型
export type ProcessIdColumnsFunction = (data: any[][]) => any[][]
export type ValidateTableDataFunction = (data: TableData) => boolean
export type FormatErrorMessageFunction = (error: UniversalTableError, details?: string) => string