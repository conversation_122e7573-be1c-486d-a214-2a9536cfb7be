# 通用表格组件使用指南

## 概述

基于 Univer 的通用表格组件，参考 SalaryTax2View 实现，专门用于处理多表格数据管理，特别适合处理包含身份证号、工号等字符型数字的场景。

## 快速开始

### 1. 安装依赖

确保项目中已安装 Univer 相关依赖：

```bash
npm install @univerjs/presets @univerjs/sheets-table @univerjs/sheets-table-ui
```

### 2. 基本使用

```vue
<template>
  <UniversalTableComponent
    :initial-data="tableData"
    @data-change="handleDataChange"
  />
</template>

<script setup>
import UniversalTableComponent from '@/components/UniversalTableComponent.vue'

const tableData = {
  '员工信息': [
    ['工号', '姓名', '身份证号'],
    ['E001', '张三', '110101199001011234']
  ]
}

const handleDataChange = (data) => {
  console.log('数据变化:', data)
}
</script>
```

## 核心特性

### 1. 字符型数字处理

组件自动识别并处理以下类型的列为文本格式：
- 身份证号
- 证件号码  
- 银行卡号
- 工号
- 纯数字列名

处理后的数据格式：
```javascript
{
  v: '110101199001011234',  // 实际值
  t: 1,                     // 文本类型
  s: null                   // 样式
}
```

### 2. 多表格管理

支持同时管理多个表格：

```javascript
const multiTableData = {
  '员工表': [
    ['工号', '姓名'],
    ['E001', '张三']
  ],
  '薪资表': [
    ['工号', '基本工资'],
    ['E001', 15000]
  ]
}
```

### 3. 数据交换

#### 父组件向子组件传递数据

```vue
<template>
  <UniversalTableComponent
    :initial-data="initialData"
    :data-provider="fetchDataFromServer"
  />
</template>

<script setup>
// 初始数据
const initialData = { /* ... */ }

// 异步数据获取
const fetchDataFromServer = async () => {
  const response = await fetch('/api/table-data')
  return await response.json()
}
</script>
```

#### 子组件向父组件传递数据

```vue
<template>
  <UniversalTableComponent
    @data-change="handleDataChange"
    @error="handleError"
  />
</template>

<script setup>
const handleDataChange = (data) => {
  // 获取到表格的所有数据
  console.log('当前表格数据:', data)
  
  // 可以保存到后端
  saveToServer(data)
}

const handleError = (error) => {
  console.error('表格错误:', error)
}
</script>
```

### 4. 组件方法调用

```vue
<template>
  <UniversalTableComponent ref="tableRef" />
</template>

<script setup>
const tableRef = ref()

// 加载新数据
const loadNewData = () => {
  tableRef.value.loadData({
    '新表格': [['列1', '列2'], ['数据1', '数据2']]
  })
}

// 获取当前数据
const getCurrentData = () => {
  const data = tableRef.value.getAllTableData()
  console.log(data)
}

// 刷新数据
const refresh = () => {
  tableRef.value.refreshData()
}

// 导出数据
const exportData = () => {
  tableRef.value.exportData()
}
</script>
```

## 高级功能

### 1. 数据验证

使用工具函数进行数据验证：

```javascript
import { validateIdNumber } from '@/utils/tableUtils'

const handleDataChange = (data) => {
  // 验证身份证号
  Object.entries(data).forEach(([tableName, tableData]) => {
    // 验证逻辑...
  })
}
```

### 2. 数据导出

```javascript
import { downloadTableDataAsCSV } from '@/utils/tableUtils'

const exportToCSV = () => {
  const data = tableRef.value.getAllTableData()
  downloadTableDataAsCSV(data, 'export.csv')
}
```

### 3. 数据统计

```javascript
import { getTableStatistics } from '@/utils/tableUtils'

const showStats = () => {
  const data = tableRef.value.getAllTableData()
  const stats = getTableStatistics(data)
  console.log('统计信息:', stats)
}
```

### 4. 数据清理

```javascript
import { cleanTableData } from '@/utils/tableUtils'

const cleanEmptyData = () => {
  const data = tableRef.value.getAllTableData()
  const cleanedData = cleanTableData(data)
  tableRef.value.loadData(cleanedData)
}
```

## 配置选项

### 1. 文本列配置

可以自定义哪些列需要处理为文本格式：

```javascript
// 在组件内部修改 textColumnConfig
const textColumnConfig = {
  keywords: ['身份证号', '证件号码', '银行卡号', '工号', '手机号'],
  processNumericHeaders: true
}
```

### 2. 样式配置

可以自定义表格样式：

```javascript
const styleConfig = {
  headerBackgroundColor: '#e3f2fd',
  headerFontWeight: 'bold',
  defaultRows: 200,
  defaultCols: 100
}
```

## 错误处理

组件提供完整的错误处理机制：

```vue
<template>
  <UniversalTableComponent
    @error="handleError"
  />
</template>

<script setup>
const handleError = (error) => {
  switch (error) {
    case 'Univer 初始化失败':
      // 处理初始化错误
      break
    case '数据加载失败':
      // 处理数据加载错误
      break
    default:
      console.error('未知错误:', error)
  }
}
</script>
```

## 性能优化建议

1. **数据量控制**：单个表格建议不超过 10000 行
2. **懒加载**：大量数据时使用 `dataProvider` 异步加载
3. **内存管理**：组件会自动清理资源，无需手动处理
4. **防抖处理**：频繁的数据变化会被自动防抖

## 常见问题

### Q: 身份证号显示为科学计数法怎么办？
A: 组件会自动处理包含"身份证号"关键词的列为文本格式，确保不会被转换为数字。

### Q: 如何添加新的文本列类型？
A: 修改组件内的 `textColumnConfig.keywords` 数组，添加新的关键词。

### Q: 如何处理大量数据？
A: 使用 `dataProvider` 函数异步加载数据，避免一次性加载过多数据。

### Q: 如何自定义表格样式？
A: 可以通过 CSS 变量或修改 `styleConfig` 来自定义样式。

## 示例项目

完整的使用示例请参考：
- `src/examples/UniversalTableExample.vue` - 基本使用示例
- `src/components/UniversalTableComponent.md` - 详细 API 文档
- `src/utils/tableUtils.ts` - 工具函数示例