<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        返回主页
      </el-button>
      <h1 class="title">统计大师 - 数据分析中心</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <el-tooltip v-for="module in modules" :key="module.key" :content="module.tooltip" placement="bottom"
          :show-after="300" effect="light" :popper-style="{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '13px',
            padding: '8px 12px',
            maxWidth: '200px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }">
          <div :class="['nav-tab', { active: activeModule === module.key }]" @click="setActiveModule(module.key)">
            <el-icon>
              <component :is="module.icon" />
            </el-icon>
            <span>{{ module.title }}</span>
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 导入现有的组件
import DataAnalysisView from '../views/DataAnalysisView.vue'
import QuickQueryView from '../views/QuickQueryView.vue'
import SapDataExportView from '../views/SapDataExportView.vue'
import TreasuryDataExportView from '../views/TreasuryDataExportView.vue'
import TreasuryInTransitPaymentView from '../views/TreasuryInTransitPaymentView.vue'
import ExceptionMaintenanceView from '../views/ExceptionMaintenanceView.vue'

const router = useRouter()
const route = useRoute()
const activeModule = ref('sap-export')

const modules = [
  {
    key: 'sap-export',
    title: 'SAP数据导出',
    icon: 'Download',
    component: SapDataExportView,
    tooltip: '导出SAP系统中的财务数据，支持多种格式和筛选条件'
  },
  {
    key: 'fip-export',
    title: '司库数据导出',
    icon: 'DocumentCopy',
    component: TreasuryDataExportView,
    tooltip: '导出司库系统的资金流水和账户信息，便于财务分析'
  },
  {
    key: 'fip-export2',
    title: '司库在途支付导出',
    icon: 'Promotion',
    component: TreasuryInTransitPaymentView,
    tooltip: '查看和导出司库系统中正在处理的支付业务数据'
  },
  {
    key: 'data-analysis',
    title: '账面看板',
    icon: 'DataAnalysis',
    component: DataAnalysisView,
    tooltip: '查看个人账面数据，包括收支明细和统计分析'
  },
  {
    key: 'quick-query',
    title: '快速查询',
    icon: 'Search',
    component: QuickQueryView,
    tooltip: '快速查询各类财务数据，支持多维度条件筛选'
  },
  {
    key: 'exception-maintenance',
    title: '异常维护',
    icon: 'Tools',
    component: ExceptionMaintenanceView,
    tooltip: '维护系统异常数据，包括清单维护、数据重导入和异常数据处理'
  },
]

const currentComponent = computed(() => {
  const module = modules.find(m => m.key === activeModule.value)
  return module ? module.component : DataAnalysisView
})

const setActiveModule = (key) => {
  activeModule.value = key
}

const goBack = () => {
  router.push('/')
}

// 根据路由参数设置activeModule
onMounted(() => {
  const activeTab = route.query.activeTab
  if (activeTab && modules.some(m => m.key === activeTab)) {
    activeModule.value = activeTab
  }
})
</script>

<style scoped>
.statistics-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  width: 100%;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.nav-tabs::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari and Opera */
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
  flex-shrink: 0;
  /* 防止tab被压缩 */
  min-width: fit-content;
  /* 确保内容完整显示 */
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  overflow: hidden;
  background: #f5f7fa;
}

.content-area>* {
  height: 100%;
  overflow: auto;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 2px;
}
</style>