<template>
  <div class="smart-reconciliation-view">
    <!-- Header -->
    <div class="header">
      <h1 class="title">智能对账抵消</h1>
    </div>

    <!-- Tabs -->
    <div class="tabs-container">
      <div class="tabs">
        <div v-for="(tab, index) in tabs" :key="tab.key" :class="['tab', { active: activeTabIndex === index }]"
          @click="switchTab(index)">
          {{ tab.name }}
          <el-button v-if="activeTabIndex === index" type="primary" size="small" @click.stop="loadTabData(tab.key)"
            :loading="loading" class="tab-execute-btn">
            {{ loading ? '加载中...' : '加载数据' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- VTable container -->
    <div class="vtable-container" ref="tableContainer">
      <NativeTableComponent v-if="currentTabData.length > 0" :data="currentTabData" :width="containerWidth" :height="containerHeight"
        :show-filter="true" :editable="false" :enable-copy-paste="true" :auto-width="true" />
      <div v-else-if="!loading" class="no-data">
        <p>{{ hasLoadedCurrentTab ? '暂无数据' : '点击"加载数据"按钮获取数据' }}</p>
      </div>
      <div v-else class="loading-data">
        <p>数据加载中...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import axios from 'axios';
import { ElButton, ElMessage } from 'element-plus';
import NativeTableComponent from '@/components/NativeTableComponent.vue';

// Tab definitions
const tabs = [
  { name: '报表抵消计算', key: 'reportOffset' },
  { name: '账面抵消情况', key: 'internalOffset' }
];

// State variables
const loading = ref(false);
const activeTabIndex = ref(0);
const reportOffsetData = ref([]);
const internalOffsetData = ref([]);
const loadedTabs = ref(new Set()); // Track which tabs have been loaded
const tableContainer = ref(null);

// Table configuration - 动态计算表格尺寸
const containerWidth = ref(1200);
const containerHeight = ref(500);

// 计算表格容器尺寸
const calculateTableDimensions = () => {
  if (tableContainer.value) {
    // 获取容器元素
    const container = tableContainer.value;
    
    // 计算可用宽度（减去内边距）
    const padding = window.innerWidth <= 768 ? 20 : 40; // 移动设备减少内边距
    const availableWidth = container.clientWidth - padding;
    
    // 计算可用高度（减去内边距）
    const availableHeight = container.clientHeight - padding;
    
    // 根据屏幕尺寸调整最小宽度和高度
    const minWidth = window.innerWidth <= 768 ? window.innerWidth - 40 : 800;
    const minHeight = window.innerWidth <= 768 ? 300 : 400;
    
    // 更新表格尺寸
    containerWidth.value = Math.max(availableWidth, minWidth);
    containerHeight.value = Math.max(availableHeight, minHeight);
    
    console.log('表格尺寸已更新:', {
      containerWidth: containerWidth.value,
      containerHeight: containerHeight.value,
      availableWidth,
      availableHeight,
      screenWidth: window.innerWidth,
      isMobile: window.innerWidth <= 768
    });
  }
};

// 监听窗口大小变化
const handleResize = () => {
  calculateTableDimensions();
};

// 组件挂载后初始化表格尺寸
onMounted(() => {
  // 初始计算
  nextTick(() => {
    calculateTableDimensions();
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
  });
});

// 组件卸载前清理资源
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);
});

// Computed properties
const currentTabData = computed(() => {
  const currentTab = tabs[activeTabIndex.value];
  if (currentTab.key === 'reportOffset') {
    return reportOffsetData.value;
  } else if (currentTab.key === 'internalOffset') {
    return internalOffsetData.value;
  }
  return [];
});

const hasLoadedCurrentTab = computed(() => {
  const currentTab = tabs[activeTabIndex.value];
  return loadedTabs.value.has(currentTab.key);
});

// UI Methods
const switchTab = (index) => {
  activeTabIndex.value = index;
  
  // 切换标签页后重新计算表格尺寸
  nextTick(() => {
    calculateTableDimensions();
  });
};

const loadTabData = async (tabKey) => {
  loading.value = true;

  try {
    // 开发环境模拟数据（用于测试VTable显示）
    const isDevelopment = false; // 启用开发模式以显示测试数据

    if (isDevelopment) {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟数据
      const mockData = {
        reportOffset: [
          ['抵消项目', '抵消科目', '本期抵消金额', '累计抵消金额', '抵消方向', '抵消依据', '状态', '备注', '审批人', '审批日期'],
          ['内部销售收入', '主营业务收入', 2500000, 8500000, '借方', '内部交易确认单', '已抵消', '第一季度抵消', '张经理', '2024-01-15'],
          ['内部采购成本', '主营业务成本', 2500000, 8500000, '贷方', '内部交易确认单', '已抵消', '第一季度抵消', '李经理', '2024-01-15'],
          ['内部利息收入', '财务费用', 150000, 450000, '借方', '内部借款协议', '已抵消', '资金拆借利息', '王经理', '2024-01-20'],
          ['内部利息支出', '财务费用', 150000, 450000, '贷方', '内部借款协议', '已抵消', '资金拆借利息', '王经理', '2024-01-20'],
          ['内部管理费', '管理费用', 300000, 900000, '借方', '内部服务协议', '已抵消', '管理费分摊', '赵经理', '2024-01-25']
        ],
        internalOffset: [
          ['交易对手', '交易类型', '交易金额', '抵消金额', '未抵消余额', '交易日期', '抵消状态', '业务部门', '负责人', '联系电话'],
          ['子公司A', '商品销售', 1500000, 1500000, 0, '2024-01-15', '完全抵消', '销售部', '张三', '13800138001'],
          ['子公司B', '服务费收入', 800000, 800000, 0, '2024-01-20', '完全抵消', '服务部', '李四', '13800138002'],
          ['子公司C', '资金拆借', 2000000, 1800000, 200000, '2024-02-01', '部分抵消', '财务部', '王五', '13800138003'],
          ['子公司D', '管理费分摊', 500000, 500000, 0, '2024-02-10', '完全抵消', '管理部', '赵六', '13800138004'],
          ['子公司E', '技术服务费', 300000, 250000, 50000, '2024-02-15', '部分抵消', '技术部', '孙七', '13800138005']
        ]
      };

      // 根据tabKey返回对应数据
      const data = mockData[tabKey] || [];

      if (tabKey === 'reportOffset') {
        reportOffsetData.value = data;
      } else if (tabKey === 'internalOffset') {
        internalOffsetData.value = data;
      }

      loadedTabs.value.add(tabKey);
      ElMessage.success('数据加载成功！（开发模式 - 使用模拟数据）');
      return;
    }

    // 生产环境：发送POST请求到后端API，传递tab参数
    const response = await axios.post('http://localhost:8000/api/reconciliation/execute', {
      tabType: tabKey
    });

    if (response.data && response.data.success) {
      const data = response.data.data || [];

      // 根据tabKey设置对应的数据
      if (tabKey === 'reportOffset') {
        reportOffsetData.value = data;
      } else if (tabKey === 'internalOffset') {
        internalOffsetData.value = data;
      }

      loadedTabs.value.add(tabKey);

      if (data.length === 0) {
        ElMessage.info('数据加载成功，但未返回任何数据。');
      } else {
        ElMessage.success('数据加载成功！');
      }
    } else {
      throw new Error(response.data?.message || '返回数据格式不正确');
    }
  } catch (error) {
    console.error('数据加载失败:', error);
    ElMessage.error(`数据加载失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
    
    // 数据加载完成后重新计算表格尺寸
    nextTick(() => {
      calculateTableDimensions();
    });
  }
};
</script>

<style scoped>
.smart-reconciliation-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f0f2f5;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.execute-btn {
  font-size: 16px;
}

.placeholder {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #909399;
  background-color: #fff;
  border-radius: 8px;
  border: 1px dashed #dcdfe6;
  min-height: 300px;
}

.placeholder p {
  margin: 0;
  font-size: 16px;
}

.results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Custom tabs */
.tabs-container {
  border-bottom: 1px solid #e8e8e8;
  background-color: white;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.tabs {
  display: flex;
  padding: 0 20px;
}

.tab {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  font-size: 16px;
  color: #595959;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  color: #1890ff;
}

.tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
  font-weight: 500;
}

.tab-execute-btn {
  margin-left: 8px;
}

/* VTable container */
.vtable-container {
  flex: 1;
  min-height: 0;
  background-color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  font-size: 16px;
}

.no-data p {
  margin: 0;
}

.loading-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #1890ff;
  font-size: 16px;
}

.loading-data p {
  margin: 0;
}

/* 确保表格组件能够完全填充容器 */
.vtable-container >>> .native-table-component {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.vtable-container >>> .table-container {
  height: 100%;
  width: 100%;
}

.vtable-container >>> .filter-panel {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.vtable-container >>> .table-container {
  flex: 1;
  min-height: 0;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .smart-reconciliation-view {
    padding: 12px;
  }
  
  .vtable-container {
    padding: 15px;
  }
}

@media (max-width: 992px) {
  .smart-reconciliation-view {
    padding: 10px;
  }
  
  .header {
    margin-bottom: 15px;
  }
  
  .title {
    font-size: 22px;
  }
  
  .vtable-container {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .smart-reconciliation-view {
    padding: 8px;
  }
  
  .header {
    margin-bottom: 12px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .title {
    font-size: 20px;
    margin-bottom: 8px;
  }
  
  .tabs {
    padding: 0 8px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .tab {
    padding: 8px 10px;
    font-size: 14px;
  }
  
  .tab-execute-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .vtable-container {
    padding: 8px;
  }
  
  /* 移动设备上表格样式调整 */
  .vtable-container >>> .filter-panel {
    padding: 10px;
  }
  
  .vtable-container >>> .filter-item {
    margin-bottom: 8px;
  }
  
  .vtable-container >>> .filter-item select,
  .vtable-container >>> .filter-item input {
    width: 100%;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .smart-reconciliation-view {
    padding: 6px;
  }
  
  .header {
    margin-bottom: 10px;
  }
  
  .title {
    font-size: 18px;
  }
  
  .tabs {
    padding: 0 5px;
  }
  
  .tab {
    padding: 6px 8px;
    font-size: 13px;
  }
  
  .tab-execute-btn {
    padding: 3px 6px;
    font-size: 11px;
  }
  
  .vtable-container {
    padding: 6px;
  }
}
</style>