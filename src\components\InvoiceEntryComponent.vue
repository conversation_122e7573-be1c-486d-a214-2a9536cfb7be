<template>
  <div class="invoice-entry-component">
    <!-- 标题栏和返回按钮 -->
    <div class="header">
      <el-button 
        type="primary" 
        :icon="ArrowLeft" 
        @click="handleBack"
        class="back-button"
      >
        返回
      </el-button>
      <h1>发票速录</h1>
    </div>

    <!-- 功能按钮区域 -->
    <div class="action-panel">
      <el-button 
        type="primary" 
        :icon="Download" 
        @click="handleGetTemplate"
        :loading="templateLoading"
      >
        获取模板
      </el-button>
      <el-button
        type="warning"
        :icon="Operation"
        @click="handleCalculateSupply"
        :loading="calculateLoading"
        :disabled="tableData.length === 0"
      >
        计算补充
      </el-button>
      <el-button 
        type="success" 
        :icon="Upload" 
        @click="handlePushData"
        :loading="pushLoading"
        :disabled="tableData.length === 0"
      >
        推送数据
      </el-button>
    </div>

    <!-- 表格展示区域 -->
    <div class="table-panel">
      <VTableComponent
        v-if="tableData.length > 0"
        :data="tableData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        :editable="true"
        :enable-copy-paste="true"
        :enable-excel-import="true"
        :enable-push-update="false"
        class="invoice-table"
        @data-change="handleDataChange"
      />
      
      <div v-else class="no-data">
        <el-empty description="请先获取模板数据" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Download, Operation, Upload } from '@element-plus/icons-vue'
import VTableComponent from './VTableComponent.vue'
import axios from 'axios';

// 定义事件
const emit = defineEmits(['back'])

// 响应式数据
const tableData = ref([])
const tableWidth = ref(800)
const tableHeight = ref(500)
const templateLoading = ref(false)
const calculateLoading = ref(false)
const pushLoading = ref(false)

// 模拟发票模板数据
const invoiceTemplateData = [
  ['发票号码', '发票日期', '供应商名称', '税号', '商品名称', '数量', '单价', '金额', '税率', '税额', '价税合计', '备注'],
  ['', '', '', '', '', '', '', '', '13%', '', '', ''],
  ['', '', '', '', '', '', '', '', '13%', '', '', ''],
  ['', '', '', '', '', '', '', '', '13%', '', '', ''],
  ['', '', '', '', '', '', '', '', '13%', '', '', ''],
  ['', '', '', '', '', '', '', '', '13%', '', '', '']
]

// 处理返回
function handleBack() {
  emit('back')
}

// 获取模板
async function handleGetTemplate() {
  templateLoading.value = true
  
  try {
    const response = await axios.post('http://localhost:8000/api/invoice/template', {});
    tableData.value = [...response.data]; // 假设后端返回的数据结构是数组
    ElMessage.success('模板获取成功')
  } catch (error) {
    ElMessage.error('获取模板失败')
  } finally {
    templateLoading.value = false
  }
}

// 计算补充
async function handleCalculateSupply() {
  calculateLoading.value = true
  
  try {
    // 模拟计算逻辑
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 自动计算金额、税额、价税合计
    const newData = [...tableData.value]
    for (let i = 1; i < newData.length; i++) {
      const row = newData[i]
      const quantity = parseFloat(row[5]) || 0
      const unitPrice = parseFloat(row[6]) || 0
      const taxRate = parseFloat(row[8]?.replace('%', '')) || 0
      
      if (quantity && unitPrice) {
        const amount = quantity * unitPrice
        const taxAmount = amount * (taxRate / 100)
        const totalAmount = amount + taxAmount
        
        row[7] = amount.toFixed(2)  // 金额
        row[9] = taxAmount.toFixed(2)  // 税额
        row[10] = totalAmount.toFixed(2)  // 价税合计
      }
    }
    
    tableData.value = newData
    ElMessage.success('计算补充完成')
  } catch (error) {
    ElMessage.error('计算补充失败')
  } finally {
    calculateLoading.value = false
  }
}

// 推送数据
async function handlePushData() {
  pushLoading.value = true
  
  try {
    // 模拟推送到后台
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('数据推送成功')
  } catch (error) {
    ElMessage.error('数据推送失败')
  } finally {
    pushLoading.value = false
  }
}

// 处理数据变化
function handleDataChange(newData) {
  tableData.value = newData
}

// 计算表格尺寸
function calculateTableSize() {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  
  tableWidth.value = Math.max(1000, windowWidth - 100)
  tableHeight.value = Math.max(500, windowHeight - 300)
}

// 窗口大小变化处理
function handleResize() {
  calculateTableSize()
}

// 组件挂载
onMounted(() => {
  calculateTableSize()
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.invoice-entry-component {
  padding: 20px;
  background: #f8f9fb;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.header h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
}

.action-panel {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.table-panel {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.invoice-table {
  border-radius: 4px;
  overflow: hidden;
}
</style>
