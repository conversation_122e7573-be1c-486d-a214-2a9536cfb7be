<template>
    <div class="financial-quotient-config">
        <el-card class="box-card fq-user-config-card">
            <template #header>
                <div class="card-header">
                    <span>用户凭证设置</span>
                </div>
            </template>
            <el-form :model="fqUserForm" label-position="top" ref="fqUserFormRef">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="用户名" prop="username">
                            <el-input v-model="fqUserForm.username" placeholder="请输入财商用户名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="密码" prop="password">
                            <el-input v-model="fqUserForm.password" type="password" placeholder="请输入财商密码"
                                show-password></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item>
                    <el-button type="primary" @click="saveFqUserSettings">保存用户凭证</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElLoading } from "element-plus";

// Financial Quotient Configuration: User Settings
const fqUserFormRef = ref();
const fqUserForm = reactive({
    username: "",
    password: "",
});

// Function to fetch financial quotient configuration
const fetchFqConfig = async () => {
    const loading = ElLoading.service({
        lock: true,
        text: "加载配置中...",
        background: "rgba(0, 0, 0, 0.7)",
    });

    try {
        const response = await fetch("http://localhost:8000/api/get-all-configs", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (!response.ok) {
            throw new Error("获取配置失败");
        }

        const configs = await response.json();

        if (configs.fqUserSettings) {
            fqUserForm.username = configs.fqUserSettings.username || "";
            fqUserForm.password = configs.fqUserSettings.password || "";
        }

        ElMessage.success("财商配置加载成功！");
    } catch (error) {
        console.error("加载财商配置失败:", error);
        ElMessage.error("加载财商配置失败，请刷新页面重试");
    } finally {
        loading.close();
    }
};

// Unified function to save configuration
const saveConfig = async (configName: string, data: any) => {
    const loading = ElLoading.service({
        lock: true,
        text: "保存中...",
        background: "rgba(0, 0, 0, 0.7)",
    });

    try {
        const response = await fetch("http://localhost:8000/api/save-config", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                [configName]: data,
            }),
        });

        if (!response.ok) {
            throw new Error("保存失败");
        }

        return true;
    } catch (error) {
        console.error(`保存${configName}失败:`, error);
        throw error;
    } finally {
        loading.close();
    }
};

const saveFqUserSettings = () => {
    fqUserFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            try {
                await saveConfig("fqUserSettings", fqUserForm);
                ElMessage.success("财商用户凭证已保存！");
            } catch (error) {
                ElMessage.error("保存财商用户凭证失败，请稍后重试");
            }
        } else {
            ElMessage.error("请检查输入项！");
            return false;
        }
    });
};

// Fetch configuration when component is mounted
onMounted(() => {
    fetchFqConfig();
});
</script>

<style scoped>
.financial-quotient-config {
    padding: 20px;
    background-color: #f4f7f6;
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
}

.box-card {
    margin-bottom: 25px;
    max-width: 100%;
    box-sizing: border-box;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
}

.el-form {
    margin-top: 10px;
}

.el-form-item {
    margin-bottom: 20px;
}
</style>