<template>
  <BaseQueryComponent
    title="内行查询"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/internal-bank'

// 查询字段配置
const queryFields = [
  {
    key: 'fiscalYear',
    label: '财年',
    type: 'select',
    placeholder: '请选择财年',
    width: '120px',
    options: [
      { label: '2024', value: '2024' },
      { label: '2023', value: '2023' },
      { label: '2022', value: '2022' },
      { label: '2021', value: '2021' }
    ]
  },
  {
    key: 'postingDate',
    label: '过帐日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'inputDate',
    label: '输入日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'voucherNumber',
    label: '凭证编号',
    type: 'text',
    placeholder: '请输入凭证编号',
    width: '180px'
  },
  {
    key: 'profitCenter',
    label: '利润中心',
    type: 'text',
    placeholder: '请输入利润中心',
    width: '150px'
  },
  {
    key: 'profitCenterDesc',
    label: '利润中心描述',
    type: 'text',
    placeholder: '请输入利润中心描述',
    width: '200px'
  },
  {
    key: 'generalLedgerAccountLongText',
    label: '总账科目长文本',
    type: 'text',
    placeholder: '请输入总账科目长文本',
    width: '200px'
  },
  {
    key: 'internalBankAmount',
    label: '内行金额',
    type: 'amount-range'
  },
  {
    key: 'internalBankCustomer',
    label: '内行客商',
    type: 'text',
    placeholder: '请输入内行客商编号',
    width: '150px'
  },
  {
    key: 'internalBankCustomerDesc',
    label: '内行客商描述',
    type: 'text',
    placeholder: '请输入内行客商名称',
    width: '200px'
  },
  {
    key: 'internalBankCustomerQuantity',
    label: '内行客商数量',
    type: 'text',
    placeholder: '请输入内行客商数量',
    width: '150px'
  },
  {
    key: 'supplier',
    label: '供应商',
    type: 'text',
    placeholder: '请输入供应商编号',
    width: '150px'
  },
  {
    key: 'supplierDesc',
    label: '供应商描述',
    type: 'text',
    placeholder: '请输入供应商名称',
    width: '200px'
  },
  {
    key: 'platformDocumentNumber',
    label: '中台单据号',
    type: 'text',
    placeholder: '请输入中台单据号',
    width: '180px'
  },
  {
    key: 'reason',
    label: '事由',
    type: 'text',
    placeholder: '请输入事由',
    width: '200px'
  }
]
</script>
