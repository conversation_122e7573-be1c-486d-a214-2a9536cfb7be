<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        返回主页
      </el-button>
      <h1 class="title">一键结账</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <el-tooltip v-for="module in modules" :key="module.key" :content="module.tooltip" placement="bottom"
          :show-after="300" effect="light" :popper-style="{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '13px',
            padding: '8px 12px',
            maxWidth: '200px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }">
          <div :class="['nav-tab', { active: activeModule === module.key }]" @click="setActiveModule(module.key)">
            <el-icon>
              <component :is="module.icon" />
            </el-icon>
            <span>{{ module.title }}</span>
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import BudgetReportTamplate from '../views/BudgetReportTamplate.vue'
import MonthlyProcessComponent from '../components/MonthlyProcessComponent.vue'
import SapDataExportView from '../views/SapDataExportView2.vue'
import BudgetReportView from '../views/BudgetReportView.vue'

const router = useRouter()
const activeModule = ref('sap-export')

const modules = [
  {
    key: 'sap-export',
    title: 'SAP数据导出',
    icon: 'Download',
    component: SapDataExportView,
    tooltip: '此处可以同步科目余额表，进行安全费取数'
  },
  {
    key: 'budget-report',
    title: '模板结账（支持自填）',
    icon: 'TrendCharts',
    component: BudgetReportTamplate,
    tooltip: '使用预设模板快速完成月度结账,安全费取数需要先获取科目余额表'
  },
  {
    key: 'budget-report2',
    title: '自动计算模板（可参考）',
    icon: 'TrendCharts',
    component: BudgetReportView,
    tooltip: '通过一系列复杂数据取数，获取结账及报表数据前置计算'
  },
  {
    key: 'monthly',
    title: '月度结账',
    icon: 'Select',
    component: MonthlyProcessComponent,
    tooltip: '执行月度财务处理流程，包括数据整理、核对和结账操作'
  }
]

const currentComponent = computed(() => {
  const module = modules.find(m => m.key === activeModule.value)
  return module ? module.component : 'div'
})

const setActiveModule = (key) => {
  activeModule.value = key
}

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.statistics-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  width: 100%;
  min-height: 0;
  /* 确保flex子元素可以收缩 */
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  /* 防止头部被压缩 */
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
  flex-shrink: 0;
  /* 防止导航被压缩 */
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  min-height: 0;
  /* 允许flex子元素收缩 */
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.content-area>* {
  flex: 1;
  min-height: 0;
  /* 确保子组件可以正确收缩 */
  overflow: auto;
  width: 100%;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 2px;
}
</style>