<template>
  <div class="fund-plan-download-container">
    <div class="download-form">
      <h2>资金计划下载</h2>
      
      <div class="form-content">
        <div class="dropdown-row">
          <div class="dropdown-item">
            <label class="dropdown-label">选择下载批次:</label>
            <el-select 
              v-model="form.batchType" 
              placeholder="请选择批次类型"
              class="dropdown-select"
            >
              <el-option label="正常计划" value="正常计划" />
              <el-option label="追加计划第一次" value="追加计划第一次" />
              <el-option label="追加计划第二次" value="追加计划第二次" />
              <el-option label="所有批次" value="所有批次" />
            </el-select>
          </div>
          
          <div class="dropdown-item">
            <label class="dropdown-label">选择批次状态:</label>
            <el-select 
              v-model="form.statusType" 
              placeholder="请选择状态类型"
              class="dropdown-select"
            >
              <el-option label="流程中" value="流程中" />
              <el-option label="结束" value="结束" />
              <el-option label="所有状态" value="所有状态" />
            </el-select>
          </div>
        </div>
        
        <div class="button-row">
          <el-button 
            type="success" 
            size="large"
            class="download-button"
            @click="downloadPlan('restart')"
            :loading="loading.restart"
          >
            <el-icon><Download /></el-icon>
            下载计划(重新开始)
          </el-button>
          
          <el-button 
            type="success" 
            size="large"
            class="download-button"
            @click="downloadPlan('continue')"
            :loading="loading.continue"
          >
            <el-icon><Download /></el-icon>
            下载计划(失败继续)
          </el-button>
        </div>
      </div>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const form = ref({
  batchType: '正常计划',
  statusType: '流程中'
})

const loading = ref({
  restart: false,
  continue: false
})

const dialogVisible = ref(false)

const downloadPlan = async (type) => {
  if (!form.value.batchType || !form.value.statusType) {
    ElMessage.warning('请选择下载批次和状态类型')
    return
  }

  loading.value[type] = true

  try {
    const functionName = type === 'restart' ? '下载计划(重新开始)' : '下载计划(失败继续)'
    
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': functionName,
        '参数': [form.value.batchType, form.value.statusType]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success(`${type === 'restart' ? '重新开始' : '失败继续'}下载任务已启动`)
    } else {
      ElMessage.error('下载任务启动失败')
    }
  } catch (error) {
    ElMessage.error('下载过程中发生错误: ' + error.message)
  } finally {
    loading.value[type] = false
  }
}
</script>

<style scoped>
.fund-plan-download-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
  position: relative;
}

.download-form {
  max-width: 800px;
  margin: 0 auto;
}

.download-form h2 {
  text-align: center;
  margin-bottom: 40px;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.dropdown-row {
  display: flex;
  gap: 30px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dropdown-label {
  font-size: 16px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.dropdown-select {
  width: 200px;
}

.dropdown-select :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 2px solid #dcdfe6;
  transition: all 0.3s ease;
}

.dropdown-select :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.dropdown-select :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.button-row {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 20px;
}

.download-button {
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  min-width: 200px;
  height: 50px;
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
  transition: all 0.3s ease;
}

.download-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(103, 194, 58, 0.4);
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
}

.download-button:active {
  transform: translateY(0);
}

.download-button .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dropdown-row {
    flex-direction: column;
    gap: 20px;
  }
  
  .dropdown-item {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .dropdown-select {
    width: 250px;
  }
  
  .button-row {
    flex-direction: column;
    align-items: center;
  }
  
  .download-button {
    width: 100%;
    max-width: 300px;
  }
}
</style>