# NativeTableComponent 分页功能说明

## 概述

NativeTableComponent 现已支持自动分页和虚拟滚动功能，可以有效提升大数据量表格的展示性能，避免渲染卡死。

## 新增功能特性

### 🚀 自动分页
- **智能启用**: 当数据量超过设定阈值时自动启用分页
- **灵活配置**: 支持自定义每页显示条数
- **完整控件**: 提供首页、上一页、下一页、末页导航
- **页码跳转**: 支持直接输入页码快速跳转
- **动态调整**: 支持运行时修改每页显示条数

### ⚡ 虚拟滚动
- **性能优化**: 大数据量时自动启用虚拟滚动
- **流畅体验**: 只渲染可见区域的数据行
- **缓冲机制**: 智能缓冲区避免滚动时闪烁
- **自动切换**: 根据数据量自动在分页和虚拟滚动间切换

### 📊 性能监控
- **实时统计**: 提供性能信息查看接口
- **状态显示**: 显示当前使用的优化策略
- **数据量提示**: 自动显示数据量和性能状态

## 使用方法

### 基础用法

```vue
<template>
  <NativeTableComponent
    :data="tableData"
    :width="1000"
    :height="600"
    :enable-pagination="true"
    :page-size="50"
    :enable-virtual-scroll="true"
    :virtual-scroll-threshold="100"
  />
</template>
```

### 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enablePagination` | Boolean | `true` | 是否启用分页功能 |
| `pageSize` | Number | `50` | 每页显示条数 |
| `enableVirtualScroll` | Boolean | `true` | 是否启用虚拟滚动 |
| `virtualScrollThreshold` | Number | `100` | 虚拟滚动启用阈值 |

### 性能策略

组件会根据数据量自动选择最佳的性能策略：

1. **数据量 ≤ pageSize**: 普通模式，显示所有数据
2. **pageSize < 数据量 ≤ virtualScrollThreshold**: 分页模式
3. **数据量 > virtualScrollThreshold**: 虚拟滚动模式

### API 方法

```javascript
// 获取组件引用
const tableRef = ref()

// 分页控制
tableRef.value.goToPage(3)           // 跳转到第3页
tableRef.value.goToFirstPage()       // 跳转到首页
tableRef.value.goToLastPage()        // 跳转到末页
tableRef.value.goToPrevPage()        // 上一页
tableRef.value.goToNextPage()        // 下一页

// 页面大小控制
tableRef.value.setPageSize(100)      // 设置每页100条
const pageSize = tableRef.value.getPageSize()  // 获取当前页面大小

// 状态查询
const isPagination = tableRef.value.isPaginationEnabled()     // 是否启用分页
const isVirtual = tableRef.value.isVirtualScrollEnabled()     // 是否启用虚拟滚动
const perfInfo = tableRef.value.getPerformanceInfo()          // 获取性能信息

// 滚动控制
tableRef.value.scrollToRow(100)      // 滚动到第100行
```

### 性能信息

```javascript
const perfInfo = tableRef.value.getPerformanceInfo()
console.log(perfInfo)
// 输出示例:
// {
//   totalRecords: 2000,
//   displayedRecords: 50,
//   isVirtualScroll: true,
//   isPagination: false,
//   currentPage: 1,
//   pageSize: 50,
//   visibleRange: { start: 0, end: 60 }
// }
```

## 测试页面

访问 `/table-pagination-test` 可以测试分页功能：

- 生成不同数据量的测试数据
- 观察自动切换的性能策略
- 测试分页和虚拟滚动的性能表现

## 性能优化建议

1. **合理设置阈值**: 根据实际需求调整 `virtualScrollThreshold`
2. **适当的页面大小**: 推荐 50-100 条记录每页
3. **避免频繁数据更新**: 大数据量时减少不必要的数据变更
4. **使用虚拟滚动**: 超过 1000 条记录建议启用虚拟滚动

## 注意事项

1. 虚拟滚动模式下，行索引会自动调整
2. 分页切换时会自动清空选择状态
3. 数据变化时会自动重新计算分页
4. 编辑功能在所有模式下都正常工作

## 兼容性

- ✅ 完全兼容现有的筛选功能
- ✅ 完全兼容编辑和复制粘贴功能
- ✅ 完全兼容导入导出功能
- ✅ 自动适应容器尺寸变化
