# 凭证查询系统 (VoucherQueryView)

## 概述

凭证查询系统是一个可视化复杂类别组成的凭证查询工具，替代了原有的综合查询和SQL可视化功能。该系统支持多面板查询、自由组合条件、灵活的逻辑连接，以及全凭证导出功能。

## 主要特性

### 🔍 多面板查询系统
- **可视化面板管理**: 支持添加、删除多个查询面板
- **面板间逻辑连接**: 支持AND/OR逻辑关系连接多个面板
- **自由组合**: 每个面板内可以添加多个查询条件

### 📋 丰富的查询条件
支持以下查询字段：
- **开始日期**: 凭证开始日期范围
- **结束日期**: 凭证结束日期范围  
- **事由**: 凭证业务事由
- **总账科目长文本**: 会计科目详细描述
- **合同编号**: 关联合同编号
- **凭证编号**: 财务凭证编号
- **中台单据号**: 系统内部单据编号
- **金额区间**: 支持最小值和最大值范围查询

### 🔧 灵活的操作符
根据字段类型提供不同的操作符：

**文本字段**:
- 等于、包含、开始于、结束于
- 不等于、为空、不为空

**数字字段**:
- 等于、大于、小于、大于等于、小于等于
- 不等于、为空、不为空

**日期字段**:
- 等于、大于、小于、大于等于、小于等于
- 不等于、为空、不为空

### 🎯 智能输入组件
- **自适应输入**: 根据字段类型自动选择合适的输入组件
- **日期选择器**: 日期字段使用专业的日期选择组件
- **数字输入**: 数字字段支持精度控制和步长设置
- **智能禁用**: "为空"/"不为空"操作符时自动禁用值输入

### 📊 高级查询功能
- **条件组合**: 面板内条件支持AND/OR逻辑连接
- **示例查询**: 提供预设的示例查询条件
- **实时验证**: 查询前自动验证条件完整性
- **结果展示**: 使用VTable组件展示查询结果

### 📤 导出功能
- **独立筛选查询**: 根据设定条件执行精确查询
- **全凭证导出**: 导出所有符合条件的凭证数据
- **Excel格式**: 支持导出为Excel格式文件

## 使用方法

### 1. 访问凭证查询
在左侧导航菜单中点击"凭证查询"进入功能页面。

### 2. 添加查询面板
- 点击"添加面板"按钮创建新的查询面板
- 每个面板可以包含多个查询条件
- 使用"删除面板"按钮移除不需要的面板

### 3. 配置查询条件
1. **选择字段**: 从下拉菜单中选择要查询的字段
2. **选择操作符**: 根据字段类型选择合适的操作符
3. **输入值**: 在相应的输入框中输入查询值
4. **添加条件**: 点击"添加条件"按钮添加更多查询条件

### 4. 设置逻辑关系
- **条件间逻辑**: 在条件行中选择AND/OR连接符
- **面板间逻辑**: 在页面顶部选择面板间的AND/OR关系

### 5. 执行查询
- 点击"独立筛选查询"按钮执行查询
- 查询结果将在下方的表格中显示
- 支持查看查询到的记录数量

### 6. 导出数据
- 点击"全凭证导出"按钮导出所有数据
- 支持Excel格式导出

## 示例查询

### 示例1: 材料采购凭证查询
```
面板1:
- 开始日期 >= 2024-01-01
- 结束日期 <= 2024-12-31  
- 事由 包含 "材料"
```

### 示例2: 大额合同付款查询
```
面板1:
- 金额区间最小值 >= 100000
- 合同编号 不为空

面板2:
- 事由 包含 "付款"
- 总账科目长文本 包含 "应付"
```

## 技术特性

### 🎨 响应式设计
- 支持桌面端和移动端访问
- 自适应布局，在不同屏幕尺寸下都有良好体验

### ⚡ 性能优化
- 查询结果限制在合理范围内
- 使用VTable组件提供高性能表格展示
- 支持大数据量的流畅操作

### 🔒 数据验证
- 查询前自动验证条件完整性
- 防止无效查询请求
- 友好的错误提示信息

### 🎯 用户体验
- 直观的可视化界面
- 智能的输入组件选择
- 实时的操作反馈

## 与原系统的对比

| 功能 | 原综合查询 | 新凭证查询 |
|------|------------|------------|
| 查询界面 | 基于Univer电子表格 | 专业的表单界面 |
| 条件设置 | 规则配置面板 | 可视化条件面板 |
| 逻辑组合 | 单一AND/OR | 多层级AND/OR |
| 字段支持 | 通用字段 | 凭证专用字段 |
| 用户体验 | 复杂配置 | 直观操作 |
| 响应式 | 有限支持 | 完全响应式 |

## 后续规划

1. **高级筛选**: 添加更多高级筛选选项
2. **保存查询**: 支持保存常用查询条件
3. **查询历史**: 记录查询历史便于重复使用
4. **批量操作**: 支持对查询结果进行批量操作
5. **图表分析**: 添加查询结果的图表分析功能

## 注意事项

1. 查询条件至少需要选择一个字段和操作符
2. "为空"和"不为空"操作符不需要输入值
3. 日期字段请使用标准日期格式
4. 大量数据查询可能需要较长时间，请耐心等待
5. 导出功能需要确保有足够的存储空间

---

*该文档描述了凭证查询系统的完整功能和使用方法。如有问题或建议，请联系开发团队。*
