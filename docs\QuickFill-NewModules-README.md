# 速填精灵新增模块说明

## 概述

基于现有的劳务派遣薪酬认领视图，新增了两个类似的功能模块：
- **发票速录**：快速录入发票信息，支持批量处理和自动计算税额
- **分包结算速录**：快速处理分包结算数据，支持批量结算和费用分摊计算

## 新增模块详情

### 1. 发票速录 (InvoiceQuickEntry)

**功能描述**：
- 快速录入发票信息
- 支持批量处理发票数据
- 自动计算税额和价税合计
- 数据验证和格式检查

**主要功能按钮**：
- **获取模板数据**：从后台获取发票录入模板
- **计算补充字段**：自动计算税额、价税合计等字段
- **提交发票数据**：将录入的发票数据提交到后台系统

**API接口**：
- `GET /api/invoice-entry/template` - 获取发票模板数据
- `POST /api/invoice-entry/calculate` - 计算补充字段
- `POST /api/start-function` - 执行发票速录提交

**预期字段**：
- 发票号码、发票日期、供应商名称、税号
- 商品名称、数量、单价、金额
- 税率、税额、价税合计、备注

### 2. 分包结算速录 (SubcontractSettlement)

**功能描述**：
- 快速处理分包结算数据
- 支持批量结算处理
- 自动计算结算金额和费用分摊
- 结算状态跟踪

**主要功能按钮**：
- **获取模板数据**：从后台获取分包结算模板
- **计算结算金额**：自动计算结算金额、税费等
- **执行批量结算**：批量处理分包结算业务

**API接口**：
- `GET /api/subcontract-settlement/template` - 获取分包结算模板数据
- `POST /api/subcontract-settlement/calculate` - 计算结算金额
- `POST /api/start-function` - 执行分包结算批量处理

**预期字段**：
- 分包商名称、合同编号、项目名称
- 结算期间、工程量、单价、结算金额
- 税率、税额、实际支付金额、结算状态

## 技术实现

### 组件结构
```
src/features/QuickFill.vue                    # 主视图，包含模块导航
src/components/InvoiceQuickEntry.vue          # 发票速录组件
src/components/SubcontractSettlement.vue      # 分包结算速录组件
```

### 设计特点
1. **统一的UI风格**：与现有的劳务派遣薪酬认领保持一致的界面设计
2. **相同的操作模式**：三个主要按钮的操作流程保持一致
3. **复用现有组件**：使用UniversalTableComponent和MessagePollingDialog
4. **响应式设计**：支持移动端和桌面端的自适应布局

### 功能特性
- **数据模板获取**：从后台获取预定义的数据模板
- **字段自动计算**：支持相关字段的自动计算和补充
- **批量处理**：支持批量数据处理和提交
- **实时反馈**：通过消息轮询对话框提供任务执行状态
- **错误处理**：完善的错误处理和用户提示机制

## 使用方法

### 发票速录使用流程
1. 在速填精灵主界面点击"发票速录"标签
2. 点击"获取模板数据"加载发票录入模板
3. 在表格中录入发票信息或导入Excel数据
4. 点击"计算补充字段"自动计算税额等字段
5. 点击"提交发票数据"将数据提交到后台系统

### 分包结算速录使用流程
1. 在速填精灵主界面点击"分包结算速录"标签
2. 点击"获取模板数据"加载分包结算模板
3. 在表格中录入结算信息或导入Excel数据
4. 点击"计算结算金额"自动计算相关金额
5. 点击"执行批量结算"批量处理结算业务

## 扩展性

### 后续可扩展功能
- 添加更多的计算规则和业务逻辑
- 支持自定义字段和模板
- 集成更多的数据验证规则
- 添加数据导出功能
- 支持审批流程集成

### 配置说明
- 所有API端点都可以通过配置文件进行调整
- 计算逻辑可以根据业务需求进行定制
- 表格字段可以通过后台模板进行动态配置

## 注意事项

1. **API依赖**：新增模块依赖后台API的支持，需要确保相应的接口已实现
2. **数据格式**：表格数据格式需要与后台API保持一致
3. **权限控制**：建议在生产环境中添加相应的权限控制机制
4. **数据验证**：建议添加更严格的数据验证和格式检查
5. **错误处理**：在生产环境中需要完善错误处理和日志记录机制

## 更新记录

- **2025-01-08**：新增发票速录和分包结算速录两个模块
- 基于劳务派遣薪酬认领视图的设计模式
- 保持统一的UI风格和操作流程
- 集成现有的表格组件和消息轮询功能