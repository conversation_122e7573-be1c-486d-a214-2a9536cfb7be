# 凭证查询系统测试指南

## 测试环境

### 前端测试
- 访问地址: http://localhost:3001/voucher-query
- 确保前端开发服务器正在运行: `npm run dev`

### 后端测试（可选）
- API地址: http://localhost:8000/api/voucher-query
- 启动命令: `python -m backend.main`
- 注意: 如果后端未启动，前端会自动使用模拟数据

## 功能测试清单

### 1. 基础界面测试
- [ ] 页面正常加载，显示"凭证查询系统"标题
- [ ] 默认显示一个查询面板
- [ ] 面板控制按钮正常显示（添加面板、加载示例查询）
- [ ] 面板间逻辑选择器正常显示（AND/OR）

### 2. 查询面板测试
- [ ] 点击"添加面板"可以成功添加新面板
- [ ] 点击"删除面板"可以删除面板（保留至少一个）
- [ ] 每个面板显示"查询面板 X"标题
- [ ] 面板内可以添加和删除查询条件

### 3. 查询条件测试

#### 字段选择测试
- [ ] 字段下拉菜单包含所有预期字段：
  - [ ] 日期范围
  - [ ] 金额范围
  - [ ] 事由
  - [ ] 总账科目长文本
  - [ ] 合同编号
  - [ ] 凭证编号
  - [ ] 中台单据号
  - [ ] 客户名称
  - [ ] 客户编码
  - [ ] 供应商名称
  - [ ] 供应商编码

#### 操作符测试
- [ ] 文本字段显示：精准匹配、模糊匹配、为空、不为空
- [ ] 日期范围字段显示：在范围内、为空、不为空
- [ ] 金额范围字段显示：在范围内、为空、不为空

#### 输入组件测试
- [ ] 日期范围：显示两个日期选择器（开始日期-结束日期）
- [ ] 金额范围：显示两个数字输入框（最小金额-最大金额）
- [ ] 文本字段：显示文本域，支持多行输入
- [ ] 提示文本正确显示"支持用逗号分隔多个值"

### 4. 示例查询测试
- [ ] 点击"加载示例查询"按钮
- [ ] 自动清空现有面板并加载示例条件
- [ ] 示例包含：日期范围、金额范围、事由模糊匹配
- [ ] 显示成功提示"已加载示例查询条件"

### 5. 查询验证测试
- [ ] 空字段验证：未选择字段时显示警告
- [ ] 空操作符验证：未选择操作符时显示警告
- [ ] 日期范围验证：日期范围不完整时显示警告
- [ ] 金额范围验证：金额范围不完整时显示警告
- [ ] 文本值验证：文本字段为空时显示警告

### 6. SQL生成测试
- [ ] 点击"查看SQL"按钮
- [ ] 弹出SQL预览对话框
- [ ] 显示格式化的SQL语句
- [ ] SQL语句包含正确的表名、字段名和条件
- [ ] 点击"复制SQL"可以复制到剪贴板

### 7. 查询执行测试
- [ ] 点击"独立筛选查询"按钮
- [ ] 显示加载状态
- [ ] 查询完成后显示结果表格
- [ ] 表格包含所有预期列（凭证编号、日期、事由等）
- [ ] 显示查询结果数量
- [ ] 表格数据格式正确

### 8. 导出功能测试
- [ ] 点击"全凭证导出"按钮
- [ ] 显示导出加载状态
- [ ] 导出完成后显示成功提示

### 9. 多值输入测试
- [ ] 在文本字段中输入"材料,设备"
- [ ] 生成的SQL使用OR逻辑连接多个值
- [ ] 精准匹配使用IN操作符
- [ ] 模糊匹配使用LIKE操作符组合

### 10. 复杂查询测试
- [ ] 创建多个面板
- [ ] 每个面板包含多个条件
- [ ] 设置不同的逻辑关系（AND/OR）
- [ ] 生成的SQL语句逻辑正确
- [ ] 查询执行正常

## SQL生成验证

### 示例1：简单文本查询
```
输入：事由 = 模糊匹配 = "材料"
期望SQL：SELECT * FROM vouchers WHERE (reason LIKE '%材料%')
```

### 示例2：多值文本查询
```
输入：事由 = 精准匹配 = "材料,设备"
期望SQL：SELECT * FROM vouchers WHERE (reason IN ('材料', '设备'))
```

### 示例3：日期范围查询
```
输入：日期范围 = 在范围内 = 2024-01-01 至 2024-12-31
期望SQL：SELECT * FROM vouchers WHERE date BETWEEN '2024-01-01' AND '2024-12-31'
```

### 示例4：金额范围查询
```
输入：金额范围 = 在范围内 = 1000 至 100000
期望SQL：SELECT * FROM vouchers WHERE amount BETWEEN 1000 AND 100000
```

### 示例5：复杂组合查询
```
面板1：日期范围 AND 金额范围
面板2：事由模糊匹配
面板间：AND
期望SQL：SELECT * FROM vouchers WHERE (date BETWEEN '...' AND '...' AND amount BETWEEN ... AND ...) AND ((reason LIKE '%...%'))
```

## 性能测试
- [ ] 大量条件组合的响应速度
- [ ] SQL生成的执行时间
- [ ] 表格渲染的流畅性
- [ ] 内存使用情况

## 兼容性测试
- [ ] Chrome浏览器
- [ ] Firefox浏览器
- [ ] Edge浏览器
- [ ] 移动端浏览器（响应式布局）

## 错误处理测试
- [ ] 网络错误时的fallback机制
- [ ] 无效SQL的处理
- [ ] 后端服务不可用时的处理
- [ ] 用户输入错误的提示

## 用户体验测试
- [ ] 界面布局合理，操作直观
- [ ] 提示信息清晰明确
- [ ] 加载状态反馈及时
- [ ] 错误信息友好易懂

## 已知问题
1. 后端服务需要Python环境，如未安装会使用模拟数据
2. 复杂SQL查询的性能优化待完善
3. 导出功能目前为模拟实现

## 测试结果记录
- 测试日期：
- 测试人员：
- 测试环境：
- 发现问题：
- 修复建议：

---

*请按照此清单逐项测试，并记录测试结果。如发现问题，请详细描述重现步骤。*
