<template>
  <BaseQueryComponent
    title="专项储备"
    :query-fields="queryFields"
    :api-endpoint="apiEndpoint"
    @back="$emit('back')"
  />
</template>

<script setup>
import BaseQueryComponent from './BaseQueryComponent.vue'

// 定义事件
defineEmits(['back'])

// API端点
const apiEndpoint = '/api/query/special-reserve'

// 查询字段配置
const queryFields = [
  {
    key: 'voucherNumber',
    label: '凭证编号',
    type: 'text',
    placeholder: '请输入凭证编号',
    width: '180px'
  },
  {
    key: 'fiscalYear',
    label: '财年',
    type: 'select',
    placeholder: '请选择财年',
    width: '120px',
    options: [
      { label: '2024', value: '2024' },
      { label: '2023', value: '2023' },
      { label: '2022', value: '2022' },
      { label: '2021', value: '2021' }
    ]
  },
  {
    key: 'profitCenter',
    label: '利润中心',
    type: 'text',
    placeholder: '请输入利润中心',
    width: '150px'
  },
  {
    key: 'profitCenterDesc',
    label: '利润中心描述',
    type: 'text',
    placeholder: '请输入利润中心描述',
    width: '200px'
  },
  {
    key: 'text',
    label: '文本',
    type: 'text',
    placeholder: '请输入文本内容',
    width: '200px'
  },
  {
    key: 'postingDate',
    label: '过帐日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'inputDate',
    label: '输入日期',
    type: 'daterange',
    width: '240px'
  },
  {
    key: 'safetyProductionFee',
    label: '安全生产费',
    type: 'amount-range'
  },
  {
    key: 'type',
    label: '类型',
    type: 'select',
    placeholder: '请选择类型',
    width: '150px',
    options: [
      { label: '提取', value: '提取' },
      { label: '使用', value: '使用' },
      { label: '结转', value: '结转' }
    ]
  }
]
</script>
