<template>
  <div class="pagination-example">
    <h2>NativeTableComponent 分页功能示例</h2>
    
    <!-- 配置面板 -->
    <div class="config-panel">
      <div class="config-item">
        <label>启用分页:</label>
        <input type="checkbox" v-model="enablePagination" />
      </div>
      <div class="config-item">
        <label>每页条数:</label>
        <select v-model="pageSize">
          <option :value="20">20</option>
          <option :value="50">50</option>
          <option :value="100">100</option>
        </select>
      </div>
      <div class="config-item">
        <label>启用虚拟滚动:</label>
        <input type="checkbox" v-model="enableVirtualScroll" />
      </div>
      <div class="config-item">
        <label>虚拟滚动阈值:</label>
        <input type="number" v-model="virtualScrollThreshold" min="50" max="500" />
      </div>
    </div>

    <!-- 数据生成按钮 -->
    <div class="data-controls">
      <button @click="generateData(50)">生成 50 条数据</button>
      <button @click="generateData(200)">生成 200 条数据</button>
      <button @click="generateData(1000)">生成 1000 条数据</button>
      <button @click="clearData">清空数据</button>
    </div>

    <!-- 状态信息 -->
    <div class="status-info" v-if="tableRef">
      <span>总记录数: {{ tableData.length > 0 ? tableData.length - 1 : 0 }}</span>
      <span>分页状态: {{ tableRef.isPaginationEnabled() ? '启用' : '禁用' }}</span>
      <span>虚拟滚动: {{ tableRef.isVirtualScrollEnabled() ? '启用' : '禁用' }}</span>
    </div>

    <!-- 表格组件 -->
    <NativeTableComponent
      ref="tableRef"
      :data="tableData"
      :width="1000"
      :height="500"
      :show-filter="true"
      :editable="true"
      :enable-copy-paste="true"
      :enable-pagination="enablePagination"
      :page-size="pageSize"
      :enable-virtual-scroll="enableVirtualScroll"
      :virtual-scroll-threshold="virtualScrollThreshold"
      @data-change="handleDataChange"
    />

    <!-- 操作按钮 -->
    <div class="action-buttons" v-if="tableRef">
      <button @click="goToFirstPage">首页</button>
      <button @click="goToPrevPage">上一页</button>
      <button @click="goToNextPage">下一页</button>
      <button @click="goToLastPage">末页</button>
      <button @click="scrollToRow(100)">滚动到第100行</button>
      <button @click="showPerformanceInfo">显示性能信息</button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import NativeTableComponent from '@/components/NativeTableComponent.vue'

// 配置参数
const enablePagination = ref(true)
const pageSize = ref(50)
const enableVirtualScroll = ref(true)
const virtualScrollThreshold = ref(100)

// 表格数据和引用
const tableData = ref([])
const tableRef = ref()

// 生成测试数据
const generateData = (count) => {
  const headers = ['ID', '姓名', '部门', '职位', '薪资', '入职日期']
  const departments = ['技术部', '销售部', '市场部', '人事部']
  const positions = ['工程师', '经理', '主管', '专员']
  
  const data = [headers]
  
  for (let i = 1; i <= count; i++) {
    data.push([
      i.toString().padStart(4, '0'),
      `员工${i}`,
      departments[Math.floor(Math.random() * departments.length)],
      positions[Math.floor(Math.random() * positions.length)],
      (Math.floor(Math.random() * 50000) + 5000).toLocaleString(),
      new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString()
    ])
  }
  
  tableData.value = data
}

// 清空数据
const clearData = () => {
  tableData.value = []
}

// 分页控制方法
const goToFirstPage = () => tableRef.value?.goToFirstPage()
const goToPrevPage = () => tableRef.value?.goToPrevPage()
const goToNextPage = () => tableRef.value?.goToNextPage()
const goToLastPage = () => tableRef.value?.goToLastPage()

// 滚动到指定行
const scrollToRow = (rowIndex) => {
  tableRef.value?.scrollToRow(rowIndex)
}

// 显示性能信息
const showPerformanceInfo = () => {
  const info = tableRef.value?.getPerformanceInfo()
  alert(JSON.stringify(info, null, 2))
}

// 数据变化处理
const handleDataChange = (data) => {
  console.log('数据已变化，记录数:', data.length - 1)
}

// 监听配置变化，动态更新表格
watch([pageSize], () => {
  if (tableRef.value) {
    tableRef.value.setPageSize(pageSize.value)
  }
})

// 初始化数据
generateData(200)
</script>

<style scoped>
.pagination-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.config-panel {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item label {
  font-weight: 500;
  color: #333;
}

.data-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.data-controls button {
  padding: 8px 16px;
  border: 1px solid #409eff;
  background: #409eff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.data-controls button:hover {
  background: #66b1ff;
}

.status-info {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.action-buttons button {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
}

.action-buttons button:hover {
  background: #f5f7fa;
  border-color: #409eff;
  color: #409eff;
}
</style>
