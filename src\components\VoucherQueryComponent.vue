<template>
  <div class="voucher-query-component">
    <!-- 标题栏和返回按钮 -->
    <div class="header">
      <el-button 
        type="primary" 
        :icon="ArrowLeft" 
        @click="handleBack"
        class="back-button"
      >
        返回
      </el-button>
      <h1>凭证查询</h1>
    </div>

    <!-- 查询面板 -->
    <div class="query-panel">
      <el-form 
        :model="queryForm" 
        :inline="true" 
        class="query-form"
        label-width="80px"
      >
        <el-form-item label="事由">
          <el-input 
            v-model="queryForm.reason" 
            placeholder="请输入事由"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="过账日期">
          <el-date-picker
            v-model="queryForm.postingDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        
        <el-form-item label="金额范围">
          <el-input 
            v-model="queryForm.amountMin" 
            placeholder="最小金额"
            type="number"
            style="width: 120px"
          />
          <span style="margin: 0 8px">-</span>
          <el-input 
            v-model="queryForm.amountMax" 
            placeholder="最大金额"
            type="number"
            style="width: 120px"
          />
        </el-form-item>
        
        <el-form-item label="凭证编号">
          <el-input 
            v-model="queryForm.voucherNumber" 
            placeholder="请输入凭证编号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="中台单据号">
          <el-input 
            v-model="queryForm.documentNumber" 
            placeholder="请输入中台单据号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            :icon="Search" 
            @click="handleQuery"
            :loading="loading"
          >
            查询
          </el-button>
          <el-button 
            :icon="Refresh" 
            @click="handleReset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 查询结果表格 -->
    <div class="result-panel">
      <div class="result-header">
        <span class="result-count">
          共找到 {{ tableData.length > 0 ? tableData.length - 1 : 0 }} 条记录
        </span>
        <el-button 
          v-if="tableData.length > 0"
          type="success" 
          :icon="Download" 
          size="small"
          @click="handleExport"
        >
          导出Excel
        </el-button>
      </div>
      
      <VTableComponent
        v-if="tableData.length > 0"
        :data="tableData"
        :width="tableWidth"
        :height="tableHeight"
        :show-filter="true"
        class="voucher-table"
      />
      
      <div v-else-if="!loading && hasQueried" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      
      <div v-else-if="!hasQueried" class="no-query">
        <el-empty description="请输入查询条件进行查询" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Search, Refresh, Download } from '@element-plus/icons-vue'
import VTableComponent from './VTableComponent.vue'

// 定义事件
const emit = defineEmits(['back'])

// 响应式数据
const queryForm = ref({
  reason: '',
  postingDate: null,
  amountMin: '',
  amountMax: '',
  voucherNumber: '',
  documentNumber: ''
})

const loading = ref(false)
const hasQueried = ref(false)
const tableData = ref([])
const tableWidth = ref(1000)
const tableHeight = ref(500)

// 模拟凭证数据
const mockVoucherData = [
  ['凭证编号', '过账日期', '事由', '借方科目', '贷方科目', '金额', '中台单据号', '制单人', '审核人'],
  ['PZ202401001', '2024-01-15', '采购材料款', '原材料', '应付账款', '50000.00', 'ZT202401001', '张三', '李四'],
  ['PZ202401002', '2024-01-16', '支付工程款', '工程施工', '银行存款', '120000.00', 'ZT202401002', '王五', '赵六'],
  ['PZ202401003', '2024-01-17', '收到预付款', '银行存款', '预收账款', '80000.00', 'ZT202401003', '钱七', '孙八'],
  ['PZ202401004', '2024-01-18', '计提折旧', '管理费用', '累计折旧', '15000.00', 'ZT202401004', '周九', '吴十'],
  ['PZ202401005', '2024-01-19', '销售收入', '应收账款', '主营业务收入', '200000.00', 'ZT202401005', '郑一', '王二']
]

// 处理返回
function handleBack() {
  emit('back')
}

// 处理查询
function handleQuery() {
  loading.value = true
  hasQueried.value = true
  
  // 模拟API调用延迟
  setTimeout(() => {
    // 这里应该调用实际的API
    // 现在使用模拟数据进行筛选
    let filteredData = [...mockVoucherData]
    
    // 根据查询条件筛选数据（简单示例）
    if (queryForm.value.reason) {
      filteredData = [
        filteredData[0], // 保留表头
        ...filteredData.slice(1).filter(row => 
          row[2] && row[2].includes(queryForm.value.reason)
        )
      ]
    }
    
    if (queryForm.value.voucherNumber) {
      filteredData = [
        filteredData[0], // 保留表头
        ...filteredData.slice(1).filter(row => 
          row[0] && row[0].includes(queryForm.value.voucherNumber)
        )
      ]
    }
    
    if (queryForm.value.documentNumber) {
      filteredData = [
        filteredData[0], // 保留表头
        ...filteredData.slice(1).filter(row => 
          row[6] && row[6].includes(queryForm.value.documentNumber)
        )
      ]
    }
    
    tableData.value = filteredData
    loading.value = false
    
    ElMessage.success(`查询完成，共找到 ${filteredData.length - 1} 条记录`)
  }, 1000)
}

// 处理重置
function handleReset() {
  queryForm.value = {
    reason: '',
    postingDate: null,
    amountMin: '',
    amountMax: '',
    voucherNumber: '',
    documentNumber: ''
  }
  tableData.value = []
  hasQueried.value = false
}

// 处理导出
function handleExport() {
  ElMessage.info('导出功能开发中...')
}

// 计算表格尺寸
function calculateTableSize() {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  
  tableWidth.value = Math.max(800, windowWidth - 100)
  tableHeight.value = Math.max(400, windowHeight - 400)
}

// 窗口大小变化处理
function handleResize() {
  calculateTableSize()
}

// 组件挂载
onMounted(() => {
  calculateTableSize()
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.voucher-query-component {
  padding: 20px;
  background: #f8f9fb;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.back-button {
  flex-shrink: 0;
}

.query-panel {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

.query-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.result-panel {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.result-count {
  font-size: 14px;
  color: #666;
}

.voucher-table {
  margin-top: 16px;
}

.no-data,
.no-query {
  padding: 40px 0;
  text-align: center;
}
</style>
