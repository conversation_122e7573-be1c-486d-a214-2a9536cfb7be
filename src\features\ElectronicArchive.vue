<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        返回主页
      </el-button>
      <h1 class="title">电子档案</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <el-tooltip v-for="module in modules" :key="module.key" :content="module.tooltip" placement="bottom"
          :show-after="300" effect="light" :popper-style="{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '13px',
            padding: '8px 12px',
            maxWidth: '200px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }">
          <div :class="['nav-tab', { active: activeModule === module.key }]" @click="setActiveModule(module.key)">
            <el-icon>
              <component :is="module.icon" />
            </el-icon>
            <span>{{ module.title }}</span>
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { defineAsyncComponent } from 'vue'

const VoucherQueryView = defineAsyncComponent(() => import('../views/VoucherQueryView.vue'))
const ArchiveManagementView = defineAsyncComponent(() => import('../views/ArchiveManagementView.vue'))
const BookManagementView = defineAsyncComponent(() => import('../views/BookManagementView.vue'))

const router = useRouter()
const activeModule = ref('voucher-query')

const modules = [
  {
    key: 'voucher-query',
    title: '凭证查询',
    icon: 'Document',
    component: VoucherQueryView,
    tooltip: '快速查询和检索财务凭证信息，支持多种查询条件和筛选方式'
  },
  {
    key: 'documents',
    title: '档案管理',
    icon: 'Folder',
    component: ArchiveManagementView,
    tooltip: '管理电子档案文件，包括文档的分类、存储和检索功能'
  },
  {
    key: 'search',
    title: '成册管理',
    icon: 'Search',
    component: BookManagementView,
    tooltip: '管理档案成册，支持档案的装订、归档和成册状态管理'
  }
]

const currentComponent = computed(() => {
  const module = modules.find(m => m.key === activeModule.value)
  return module ? module.component : 'div'
})

const setActiveModule = (key) => {
  activeModule.value = key
}

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.statistics-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  width: 100%;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  min-height: 0;
  background: #f5f7fa;
}

.content-area>* {
  min-height: 100%;
  overflow: auto;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 2px;
}
</style>