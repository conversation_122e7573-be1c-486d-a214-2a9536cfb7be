<template>
  <div class="financial-special-payment">
    <!-- 操作按钮行 -->
    <div class="action-buttons">
      <el-button type="primary" @click="getTemplateData" :loading="isLoadingTemplate" icon="Download">
        查询
      </el-button>
      <el-button type="success" @click="updateData" :loading="isUpdatingData" icon="Refresh">
        保存
      </el-button>
      <el-button type="warning" @click="executeSpecialPayment" :loading="isProcessingPayment" icon="CreditCard">
        执行特殊支付
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <UniversalTableComponent 
        ref="tableRef" 
        :initial-data="tableData" 
        :data-provider="dataProvider"
        workbook-name="财商特殊支付" 
        @data-change="handleDataChange" 
        @error="handleError"
        @initialized="handleTableInitialized" 
      />
    </div>

    <!-- 消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" @dialog-closed="handleDialogClosed"/>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import UniversalTableComponent from './UniversalTableComponent.vue'
import MessagePollingDialog from './MessagePollingDialog.vue'

// 响应式数据
const tableRef = ref(null)
const tableData = ref({})
const dialogVisible = ref(false)

// 加载状态
const isLoadingTemplate = ref(false)
const isUpdatingData = ref(false)
const isProcessingPayment = ref(false)

// 获取模板数据
const getTemplateData = async () => {
  try {
    isLoadingTemplate.value = true

    const response = await fetch('http://127.0.0.1:8000/api/financial-special-payment/template', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      // 直接赋值，让Vue的响应式系统处理
      tableData.value = result.data

      ElMessage.success('模板数据获取成功')
    } else {
      throw new Error(result.message || '获取模板数据失败')
    }
  } catch (error) {
    console.error('获取模板数据失败:', error)
    ElMessage.error('获取模板数据失败: ' + error.message)
  } finally {
    isLoadingTemplate.value = false
  }
}

// 更新数据
const updateData = async () => {
  try {
    isUpdatingData.value = true

    // 获取当前表格数据
    const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

    const response = await fetch('http://127.0.0.1:8000/api/financial-special-payment/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        currentData: currentData,
        timestamp: new Date().toISOString()
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      // 更新表格数据
      if (result.data) {
        tableData.value = result.data
      }

      ElMessage.success('数据更新成功')
    } else {
      throw new Error(result.message || '数据更新失败')
    }
  } catch (error) {
    console.error('数据更新失败:', error)
    ElMessage.error('数据更新失败: ' + error.message)
  } finally {
    isUpdatingData.value = false
  }
}

// 执行特殊支付
const executeSpecialPayment = async () => {
  try {
    isProcessingPayment.value = true

    // 获取当前表格数据
    const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

    // 验证数据
    if (!currentData || Object.keys(currentData).length === 0) {
      ElMessage.warning('请先获取模板数据')
      return
    }

    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '批量特殊支付',
        '参数': []
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('特殊支付任务已启动')
    } else {
      throw new Error(result.message || '特殊支付启动失败')
    }
  } catch (error) {
    console.error('特殊支付失败:', error)
    ElMessage.error('特殊支付失败: ' + error.message)
  } finally {
    isProcessingPayment.value = false
  }
}

// 数据提供函数（用于刷新）
const dataProvider = async () => {
  const response = await fetch('http://127.0.0.1:8000/api/financial-special-payment/template', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const result = await response.json()

  if (result.code === 200) {
    return result.data
  } else {
    throw new Error(result.message || '获取数据失败')
  }
}

// 事件处理
const handleDataChange = (data) => {
  console.log('表格数据变化:', data)
}

const handleError = (error) => {
  console.error('表格错误:', error)
  ElMessage.error(error)
}

const handleTableInitialized = () => {
  console.log('表格初始化完成')
}

const handleDialogClosed = () => {
    console.log('对话框已关闭，重新获取模板数据')
    getTemplateData()
}

// 组件挂载后自动执行getTemplateData
onMounted(() => {
    getTemplateData()
})
</script>

<style scoped>
.financial-special-payment {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 60px);
  /* 减去顶部导航栏高度 */
  background: #f5f7fa;
}

.action-buttons {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.action-buttons .el-button {
  min-width: 120px;
}

.table-container {
  flex: 1;
  overflow: hidden;
  background: white;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-wrap: wrap;
    gap: 8px;
    padding: 12px 16px;
  }

  .action-buttons .el-button {
    flex: 1;
    min-width: auto;
  }
}
</style>