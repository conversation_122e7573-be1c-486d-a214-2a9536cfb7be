<template>
  <div class="reverse-factoring-container">
    <!-- 根据文件生成模板 -->
    <div class="function-section">
      <h3>根据文件生成模板</h3>
      <el-form :model="templateForm" label-width="140px">
        <el-form-item label="待支付列表文件">
          <div class="file-input-group">
            <el-input v-model="templateForm.paymentListPath" placeholder="请选择待支付列表文件路径" readonly />
            <el-button @click="selectPaymentListFile" :loading="paymentListLoading">选择文件</el-button>
          </div>
        </el-form-item>

        <el-form-item label="三局严选文件">
          <div class="file-input-group">
            <el-input v-model="templateForm.selectedFilePath" placeholder="请选择三局严选文件路径" readonly />
            <el-button @click="selectSelectedFile" :loading="selectedFileLoading">选择文件</el-button>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="generateTemplateFromFiles" :loading="generateFromFilesLoading">
            {{ generateFromFilesLoading ? '生成中...' : '生成模板' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 直接生成模板 -->
    <div class="function-section">
      <h3>直接生成模板</h3>
      <el-form label-width="140px">
        <el-form-item>
          <el-button type="primary" @click="generateDirectTemplate" :loading="directTemplateLoading">
            {{ directTemplateLoading ? '生成中...' : '直接生成模板' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 反向保理填单 -->
    <div class="function-section">
      <h3>反向保理填单</h3>
      <el-form label-width="140px">
        <el-form-item>
          <el-button type="primary" @click="performReverseFactoring" :loading="factoringLoading">
            {{ factoringLoading ? '填单中...' : '开始填单' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

const templateForm = ref({
  paymentListPath: '',
  selectedFilePath: ''
})

const paymentListLoading = ref(false)
const selectedFileLoading = ref(false)
const generateFromFilesLoading = ref(false)
const directTemplateLoading = ref(false)
const factoringLoading = ref(false)
const dialogVisible = ref(false)

// 选择待支付列表文件
const selectPaymentListFile = async () => {
  paymentListLoading.value = true
  
  try {
    const response = await fetch('http://127.0.0.1:8000/api/get-file-path', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: '选择待支付列表文件',
        fileTypes: [
          { name: 'Excel文件', extensions: ['xlsx', 'xls'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      })
    })

    if (response.ok) {
      const data = await response.json()
      if (data.filePath) {
        templateForm.value.paymentListPath = data.filePath
        ElMessage.success('文件路径已选择')
      }
    } else {
      ElMessage.error('获取文件路径失败')
    }
  } catch (error) {
    ElMessage.error('选择文件时发生错误: ' + error.message)
  } finally {
    paymentListLoading.value = false
  }
}

// 选择三局严选文件
const selectSelectedFile = async () => {
  selectedFileLoading.value = true
  
  try {
    const response = await fetch('http://127.0.0.1:8000/api/get-file-path', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: '选择三局严选文件',
        fileTypes: [
          { name: 'Excel文件', extensions: ['xlsx', 'xls'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      })
    })

    if (response.ok) {
      const data = await response.json()
      if (data.filePath) {
        templateForm.value.selectedFilePath = data.filePath
        ElMessage.success('文件路径已选择')
      }
    } else {
      ElMessage.error('获取文件路径失败')
    }
  } catch (error) {
    ElMessage.error('选择文件时发生错误: ' + error.message)
  } finally {
    selectedFileLoading.value = false
  }
}

// 根据文件生成模板
const generateTemplateFromFiles = async () => {
  if (!templateForm.value.paymentListPath || !templateForm.value.selectedFilePath) {
    ElMessage.warning('请先选择待支付列表文件和三局严选文件')
    return
  }

  generateFromFilesLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '根据文件生成反向保理模板',
        '参数': [templateForm.value.paymentListPath, templateForm.value.selectedFilePath]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('反向保理模板生成任务已启动')
    } else {
      ElMessage.error('模板生成失败')
    }
  } catch (error) {
    ElMessage.error('生成模板时发生错误: ' + error.message)
  } finally {
    generateFromFilesLoading.value = false
  }
}

// 直接生成模板
const generateDirectTemplate = async () => {
  directTemplateLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '直接生成反向保理模板',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('反向保理模板直接生成任务已启动')
    } else {
      ElMessage.error('直接生成模板失败')
    }
  } catch (error) {
    ElMessage.error('直接生成模板时发生错误: ' + error.message)
  } finally {
    directTemplateLoading.value = false
  }
}

// 反向保理填单
const performReverseFactoring = async () => {
  factoringLoading.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '执行反向保理提单',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('反向保理填单任务已启动')
    } else {
      ElMessage.error('反向保理填单失败')
    }
  } catch (error) {
    ElMessage.error('填单过程中发生错误: ' + error.message)
  } finally {
    factoringLoading.value = false
  }
}
</script>

<style scoped>
.reverse-factoring-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: auto;
}

.function-section {
  max-width: 600px;
  margin: 0 auto 40px auto;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.function-section h3 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.function-section:last-of-type {
  margin-bottom: 0;
}

.file-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.file-input-group .el-input {
  flex: 1;
}

.file-input-group .el-button {
  flex-shrink: 0;
}
</style>