<template>
  <div class="statistics-container">
    <!-- 返回按钮和标题 -->
    <div class="header">
      <el-button @click="goBack" type="primary" size="small">
        <el-icon>
          <ArrowLeft />
        </el-icon>
        返回主页
      </el-button>
      <h1 class="title">智能稽核 - 智能审核验证</h1>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-nav">
      <div class="nav-tabs">
        <el-tooltip v-for="module in modules" :key="module.key" :content="module.tooltip" placement="bottom"
          :show-after="300" effect="light" :popper-style="{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontSize: '13px',
            padding: '8px 12px',
            maxWidth: '200px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }">
          <div :class="['nav-tab', { active: activeModule === module.key }]" @click="setActiveModule(module.key)">
            <el-icon>
              <component :is="module.icon" />
            </el-icon>
            <span>{{ module.title }}</span>
          </div>
        </el-tooltip>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <div v-if="activeModule === 'audit'" class="audit-container">
        <div class="audit-form">
          <h2>综合稽核</h2>

          <!-- 数据同步提示 -->
          <div class="sync-notice">
            <el-alert title="稽核前请按下方步骤依次同步数据" type="warning" :closable="false" show-icon>
            </el-alert>
          </div>

          <!-- 数据导出按钮组 -->
          <div class="export-buttons">
            <el-button type="success" @click="navigateToSapExport" :icon="Download">
              SAP数据导出
            </el-button>
            <el-button type="info" @click="navigateToTreasuryExport" :icon="DocumentCopy">
              司库数据导出
            </el-button>
          </div>

          <!-- 稽核按钮 -->
          <div class="button-container">
            <el-button type="primary" @click="startAudit" :loading="loading">
              {{ loading ? '稽核中...' : '综合稽核' }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 科目对照 -->
      <div v-if="activeModule === 'subject-mapping'" class="subject-mapping-content">
        <div class="subject-mapping-header">
          <h2>科目对照管理</h2>
          <div class="subject-mapping-actions">
            <el-button type="primary" @click="importExcelToBackend" :loading="importingExcel" :icon="Upload">
              {{ importingExcel ? '导入中...' : '直接导入Excel到后台' }}
            </el-button>
            <el-button type="success" @click="getRemoteReference" :loading="gettingReference" :icon="Download">
              {{ gettingReference ? '获取中...' : '获取远程参考' }}
            </el-button>
          </div>
        </div>
        <div class="subject-mapping-component">
          <SubjectMappingQueryComponent @back="handleSubjectMappingBack" />
        </div>
      </div>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Upload, Download } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'
import SubjectMappingQueryComponent from '@/components/SubjectMappingQueryComponent.vue'

const router = useRouter()
const activeModule = ref('audit')
const dialogVisible = ref(false)
const loading = ref(false)

// 科目对照相关数据
const importingExcel = ref(false)
const gettingReference = ref(false)

const modules = [
  {
    key: 'audit',
    title: '综合稽核',
    icon: 'Monitor',
    tooltip: '执行全面的财务数据稽核，需要先同步科目余额表，配置科目分类'
  },
  {
    key: 'subject-mapping',
    title: '科目对照',
    icon: 'Document',
    tooltip: '可按默认设置配置，如果特殊需求，请自行配置'
  }
]

const startAudit = async () => {
  loading.value = true

  try {
    // 发送POST请求启动综合稽核
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '综合稽核',
        '参数': []
      })
    })

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('综合稽核任务已启动')
    } else {
      ElMessage.error('综合稽核启动失败')
    }
  } catch (error) {
    ElMessage.error('稽核过程中发生错误: ' + error.message)
  } finally {
    loading.value = false
  }
}

const setActiveModule = (key) => {
  activeModule.value = key
}

const goBack = () => {
  router.push('/')
}

// 导航到SAP数据导出
const navigateToSapExport = () => {
  router.push({
    path: '/statistics',
    query: { activeTab: 'sap-export' }
  })
}

// 导航到司库数据导出
const navigateToTreasuryExport = () => {
  router.push({
    path: '/statistics',
    query: { activeTab: 'fip-export' }
  })
}

// 直接导入Excel到后台
const importExcelToBackend = async () => {
  importingExcel.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/import-excel-update-db', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        'tableName': '科目对照'
      })
    })

    if (response.ok) {
      ElMessage.success('Excel导入成功')
    } else {
      ElMessage.error('Excel导入失败')
    }
  } catch (error) {
    ElMessage.error('Excel导入过程中发生错误: ' + error.message)
  } finally {
    importingExcel.value = false
  }
}

// 获取远程参考
const getRemoteReference = async () => {
  gettingReference.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '获取科目对照远程参考',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('获取远程参考任务已启动')
    } else {
      ElMessage.error('获取远程参考失败')
    }
  } catch (error) {
    ElMessage.error('获取远程参考过程中发生错误: ' + error.message)
  } finally {
    gettingReference.value = false
  }
}

// 处理科目对照组件返回
const handleSubjectMappingBack = () => {
  // 在智能稽核页面中，不需要处理返回，因为已经在稽核页面内部
  console.log('科目对照组件返回事件')
}
</script>

<style scoped>
.statistics-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  position: relative;
}

.header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modules-nav {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 20px;
  flex-shrink: 0;
}

.nav-tabs {
  display: flex;
  gap: 2px;
  overflow-x: auto;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  color: #5a6c7d;
  font-size: 14px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.nav-tab.active {
  background: #e3f2fd;
  color: #1976d2;
  border-bottom-color: #1976d2;
  font-weight: 600;
}

.nav-tab .el-icon {
  font-size: 16px;
}

.content-area {
  flex: 1;
  overflow: hidden;
  background: #f5f7fa;
  height: 100%;
}

.content-area>* {
  height: 100%;
  overflow: auto;
}

.audit-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: calc(100% - 40px);
  margin: 20px;
  overflow: auto;
  position: relative;
}

.audit-form {
  max-width: 500px;
  margin: 0 auto;
  padding-top: 50px;
}

.audit-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}

/* 科目对照内容 */
.subject-mapping-content {
  padding: 20px;
  background: white;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: calc(100vh - 200px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.subject-mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8e8e8;
}

.subject-mapping-header h2 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.subject-mapping-actions {
  display: flex;
  gap: 12px;
}

.subject-mapping-actions .el-button {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.subject-mapping-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.subject-mapping-component {
  flex: 1;
  overflow: hidden;
  background: #f8f9fb;
  border-radius: 6px;
  padding: 0;
}

.subject-mapping-component :deep(.subject-mapping-query-component) {
  padding: 0;
  background: transparent;
  min-height: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.subject-mapping-component :deep(.header) {
  display: none;
  /* 隐藏组件内部的标题和返回按钮 */
}

.subject-mapping-component :deep(.query-panel),
.subject-mapping-component :deep(.action-panel),
.subject-mapping-component :deep(.result-panel) {
  margin: 0 0 16px 0;
}

.subject-mapping-component :deep(.result-panel) {
  flex: 1;
  overflow: hidden;
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.button-container .el-button {
  min-width: 120px;
}

.sync-notice {
  margin-bottom: 20px;
}

.export-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.export-buttons .el-button {
  min-width: 140px;
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.nav-tabs::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>