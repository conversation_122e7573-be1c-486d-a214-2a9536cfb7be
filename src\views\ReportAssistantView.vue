<template>
  <div class="report-assistant-container">
    <!-- 期初数补全模块 -->
    <div class="module-section">
      <div class="module-header">
        <el-icon class="module-icon">
          <Document />
        </el-icon>
        <h3>期初数补全</h3>
      </div>

      <div class="path-selection">
        <div class="path-item">
          <label>上年度报表路径：</label>
          <div class="path-input-group">
            <el-input v-model="lastYearPath" placeholder="请选择上年度报表路径" readonly class="path-input" />
            <el-button @click="selectLastYearPath" type="primary" :loading="selectingLastYear" class="select-btn">
              <el-icon>
                <FolderOpened />
              </el-icon>
              选择文件
            </el-button>
          </div>
        </div>

        <div class="path-item">
          <label>本年度报表路径：</label>
          <div class="path-input-group">
            <el-input v-model="currentYearPath" placeholder="请选择本年度报表路径" readonly class="path-input" />
            <el-button @click="selectCurrentYearPath" type="primary" :loading="selectingCurrentYear" class="select-btn">
              <el-icon>
                <FolderOpened />
              </el-icon>
              选择文件
            </el-button>
          </div>
        </div>
      </div>

      <div class="action-section">
        <el-button @click="executeInitialDataCompletion" type="success" size="large" :loading="executingCompletion"
          :disabled="!lastYearPath || !currentYearPath" class="execute-btn">
          <el-icon>
            <Tools />
          </el-icon>
          {{ executingCompletion ? '执行中...' : '执行期初数补全' }}
        </el-button>
      </div>
    </div>

    <!-- 坏账分析模块 -->
    <div class="module-section">
      <div class="module-header">
        <el-icon class="module-icon">
          <TrendCharts />
        </el-icon>
        <h3>坏账分析</h3>
      </div>

      <div class="analysis-description">
        <p>先用sap坏账计提表面的行excel导出，然后上传一下上年度报表表格，执行坏账分析，选择导出的excel再选择存储位置</p>
      </div>

      <div class="action-section">
        <el-button @click="executeBadDebtAnalysis" type="warning" size="large" :loading="executingAnalysis"
          class="execute-btn">
          <el-icon>
            <DataAnalysis />
          </el-icon>
          {{ executingAnalysis ? '执行中...' : '执行坏账分析' }}
        </el-button>
      </div>
    </div>

    <!-- 引入消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, FolderOpened, Tools, TrendCharts, DataAnalysis } from '@element-plus/icons-vue'
import MessagePollingDialog from '@/components/MessagePollingDialog.vue'

// 响应式数据
const lastYearPath = ref('')
const currentYearPath = ref('')
const selectingLastYear = ref(false)
const selectingCurrentYear = ref(false)
const executingCompletion = ref(false)
const executingAnalysis = ref(false)
const dialogVisible = ref(false)

// 选择上年度报表路径
const selectLastYearPath = async () => {
  selectingLastYear.value = true
  try {
    const response = await fetch('http://localhost:8000/api/report-assistant/select-last-year-path', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        lastYearPath.value = data.path
        ElMessage.success('上年度报表路径选择成功')
      } else {
        ElMessage.error(data.message || '选择路径失败')
      }
    } else {
      ElMessage.error('选择路径请求失败')
    }
  } catch (error) {
    console.error('选择上年度路径失败:', error)
    ElMessage.error('选择路径失败')
  } finally {
    selectingLastYear.value = false
  }
}

// 选择本年度报表路径
const selectCurrentYearPath = async () => {
  selectingCurrentYear.value = true
  try {
    const response = await fetch('http://localhost:8000/api/report-assistant/select-current-year-path', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        currentYearPath.value = data.path
        ElMessage.success('本年度报表路径选择成功')
      } else {
        ElMessage.error(data.message || '选择路径失败')
      }
    } else {
      ElMessage.error('选择路径请求失败')
    }
  } catch (error) {
    console.error('选择本年度路径失败:', error)
    ElMessage.error('选择路径失败')
  } finally {
    selectingCurrentYear.value = false
  }
}

// 执行期初数补全
const executeInitialDataCompletion = async () => {
  if (!lastYearPath.value || !currentYearPath.value) {
    ElMessage.warning('请先选择上年度和本年度报表路径')
    return
  }

  executingCompletion.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '期初数补全',
        '参数': [lastYearPath.value, currentYearPath.value]
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('期初数补全任务已启动')
    } else {
      ElMessage.error('期初数补全启动失败')
    }
  } catch (error) {
    ElMessage.error('执行过程中发生错误: ' + error.message)
  } finally {
    executingCompletion.value = false
  }
}

// 获取初始路径
const fetchInitialPaths = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/report-assistant/paths', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      lastYearPath.value = data.lastYearPath || ''
      currentYearPath.value = data.currentYearPath || ''
    }
  } catch (error) {
    console.error('获取初始路径失败:', error)
  }
}

// 执行坏账分析
const executeBadDebtAnalysis = async () => {
  executingAnalysis.value = true

  try {
    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '坏账分析',
        '参数': []
      })
    })

    if (response.ok) {
      dialogVisible.value = true
      ElMessage.success('坏账分析任务已启动')
    } else {
      ElMessage.error('坏账分析启动失败')
    }
  } catch (error) {
    ElMessage.error('执行过程中发生错误: ' + error.message)
  } finally {
    executingAnalysis.value = false
  }
}

// 组件挂载时获取初始路径
onMounted(() => {
  fetchInitialPaths()
})
</script>

<style scoped>
.report-assistant-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f5f7fa;
  min-height: 100%;
}

.module-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.module-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f2f5;
}

.module-icon {
  font-size: 24px;
  color: #1976d2;
}

.module-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.path-selection {
  margin-bottom: 24px;
}

.path-item {
  margin-bottom: 16px;
}

.path-item label {
  display: block;
  margin-bottom: 8px;
  color: #5a6c7d;
  font-weight: 500;
  font-size: 14px;
}

.path-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.path-input {
  flex: 1;
}

.select-btn {
  min-width: 120px;
  height: 40px;
}

.action-section {
  display: flex;
  justify-content: center;
  padding-top: 16px;
}

.execute-btn {
  min-width: 160px;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

.analysis-description {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.analysis-description p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-assistant-container {
    padding: 16px;
  }

  .module-section {
    padding: 16px;
  }

  .path-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .select-btn {
    min-width: auto;
  }
}

/* 按钮悬停效果 */
.execute-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.select-btn:hover {
  transform: translateY(-1px);
}

/* 加载状态样式 */
.execute-btn:loading,
.select-btn:loading {
  opacity: 0.7;
}
</style>